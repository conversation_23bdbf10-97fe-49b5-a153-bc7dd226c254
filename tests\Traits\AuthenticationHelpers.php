<?php

namespace Tests\Traits;

use App\Models\User;
use Lara<PERSON>\Sanctum\Sanctum;
use Illuminate\Support\Facades\Hash;

trait AuthenticationHelpers
{
    /**
     * Créer un utilisateur de test
     */
    protected function createTestUser(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'employee'
        ], $attributes));
    }

    /**
     * Créer un administrateur de test
     */
    protected function createTestAdmin(array $attributes = []): User
    {
        return User::factory()->create(array_merge([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ], $attributes));
    }

    /**
     * Authentifier un utilisateur avec Sanctum
     */
    protected function authenticateUser(User $user = null): User
    {
        $user = $user ?: $this->createTestUser();
        Sanctum::actingAs($user);
        return $user;
    }

    /**
     * Authentifier un administrateur avec Sanctum
     */
    protected function authenticateAdmin(User $admin = null): User
    {
        $admin = $admin ?: $this->createTestAdmin();
        Sanctum::actingAs($admin);
        return $admin;
    }

    /**
     * Créer un token d'authentification pour un utilisateur
     */
    protected function createAuthToken(User $user, string $tokenName = 'test-token'): string
    {
        return $user->createToken($tokenName)->plainTextToken;
    }

    /**
     * Obtenir les headers d'authentification avec Bearer token
     */
    protected function getAuthHeaders(string $token): array
    {
        return [
            'Authorization' => 'Bearer ' . $token,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json'
        ];
    }

    /**
     * Obtenir les headers JSON standards
     */
    protected function getJsonHeaders(): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json'
        ];
    }

    /**
     * Effectuer une requête authentifiée
     */
    protected function authenticatedRequest(string $method, string $uri, array $data = [], User $user = null)
    {
        $user = $user ?: $this->createTestUser();
        $token = $this->createAuthToken($user);
        
        return $this->withHeaders($this->getAuthHeaders($token))
                    ->json($method, $uri, $data);
    }

    /**
     * Effectuer une requête GET authentifiée
     */
    protected function authenticatedGet(string $uri, User $user = null)
    {
        return $this->authenticatedRequest('GET', $uri, [], $user);
    }

    /**
     * Effectuer une requête POST authentifiée
     */
    protected function authenticatedPost(string $uri, array $data = [], User $user = null)
    {
        return $this->authenticatedRequest('POST', $uri, $data, $user);
    }

    /**
     * Effectuer une requête PUT authentifiée
     */
    protected function authenticatedPut(string $uri, array $data = [], User $user = null)
    {
        return $this->authenticatedRequest('PUT', $uri, $data, $user);
    }

    /**
     * Effectuer une requête DELETE authentifiée
     */
    protected function authenticatedDelete(string $uri, User $user = null)
    {
        return $this->authenticatedRequest('DELETE', $uri, [], $user);
    }

    /**
     * Vérifier la structure de réponse d'authentification
     */
    protected function assertAuthenticationResponse($response, bool $shouldBeAuthenticated = true): void
    {
        if ($shouldBeAuthenticated) {
            $response->assertStatus(200)
                    ->assertJsonStructure([
                        'success',
                        'data' => [
                            'user' => [
                                'id',
                                'name', 
                                'email',
                                'role'
                            ],
                            'token'
                        ],
                        'message'
                    ]);
        } else {
            $response->assertStatus(401)
                    ->assertJsonStructure([
                        'success',
                        'message'
                    ]);
        }
    }

    /**
     * Vérifier qu'une requête nécessite une authentification
     */
    protected function assertRequiresAuthentication(string $method, string $uri, array $data = []): void
    {
        $response = $this->withHeaders($this->getJsonHeaders())
                         ->json($method, $uri, $data);
        
        $response->assertStatus(401);
    }
}
