<?php

namespace Tests\Feature\Log;

use Tests\TestCase;
use Tests\Traits\DatabaseIntegrationTrait;
use App\Models\Log;
use App\Models\User;
use Illuminate\Support\Facades\Log as LaravelLog;
use <PERSON><PERSON>\Sanctum\Sanctum;
use Carbon\Carbon;

class LogIntegrationTest extends TestCase
{
    use DatabaseIntegrationTrait;

    /**
     * Test system automatically creates logs for user actions.
     */
    public function test_system_creates_logs_for_user_actions(): void
    {
        LaravelLog::info('Testing system creates logs for user actions');

        // Create employee
        $employee = $this->createTestUser();
        Sanctum::actingAs($employee);

        // Perform an action that should create a log (e.g., login)
        $response = $this->postJson('/api/login', [
            'email' => $employee->email,
            'password' => 'password'
        ]);

        // Verify log is created in database
        $this->assertDatabaseHas('logs', [
            'user_id' => $employee->id,
            'action' => 'login'
        ]);

        // Get the created log
        $log = Log::where('user_id', $employee->id)
                  ->where('action', 'login')
                  ->first();
        
        $this->assertNotNull($log);
        $this->assertNotNull($log->details);

        LaravelLog::info('System log creation test completed successfully', [
            'employee_id' => $employee->id,
            'log_id' => $log->id
        ]);
    }

    /**
     * Test admin can view all system logs.
     */
    public function test_admin_can_view_all_system_logs(): void
    {
        LaravelLog::info('Testing admin can view all system logs');

        // Create admin and employees
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);

        // Create logs manually for testing
        $log1 = Log::create([
            'user_id' => $employee1->id,
            'action' => 'pointage_start',
            'details' => json_encode(['site_id' => 1, 'latitude' => 33.8869, 'longitude' => -9.5375])
        ]);

        $log2 = Log::create([
            'user_id' => $employee2->id,
            'action' => 'pointage_end',
            'details' => json_encode(['site_id' => 1, 'duration' => '08:00:00'])
        ]);

        $log3 = Log::create([
            'user_id' => $admin->id,
            'action' => 'employee_created',
            'details' => json_encode(['created_employee_id' => $employee1->id])
        ]);

        Sanctum::actingAs($admin);

        // Request all logs
        $response = $this->getJson('/api/admin/logs');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id', 'user_id', 'action', 'details', 'created_at',
                             'user' => ['id', 'name', 'email']
                         ]
                     ]
                 ]);

        // Verify all logs are in response
        $responseData = $response->json();
        $logIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($log1->id, $logIds);
        $this->assertContains($log2->id, $logIds);
        $this->assertContains($log3->id, $logIds);

        // Verify data comes from database
        $this->assertDatabaseHasRecord('logs', ['id' => $log1->id, 'action' => 'pointage_start']);
        $this->assertDatabaseHasRecord('logs', ['id' => $log2->id, 'action' => 'pointage_end']);
        $this->assertDatabaseHasRecord('logs', ['id' => $log3->id, 'action' => 'employee_created']);

        LaravelLog::info('Admin view all logs test completed successfully');
    }

    /**
     * Test admin can filter logs by action type.
     */
    public function test_admin_can_filter_logs_by_action_type(): void
    {
        LaravelLog::info('Testing admin can filter logs by action type');

        // Create admin and employee
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser();

        // Create logs with different actions
        $loginLog = Log::create([
            'user_id' => $employee->id,
            'action' => 'login',
            'details' => json_encode(['ip' => '127.0.0.1'])
        ]);

        $pointageLog = Log::create([
            'user_id' => $employee->id,
            'action' => 'pointage_start',
            'details' => json_encode(['site_id' => 1])
        ]);

        $logoutLog = Log::create([
            'user_id' => $employee->id,
            'action' => 'logout',
            'details' => json_encode(['session_duration' => '02:30:00'])
        ]);

        Sanctum::actingAs($admin);

        // Request logs filtered by action
        $response = $this->getJson('/api/admin/logs?action=pointage_start');

        // Assert successful response
        $response->assertStatus(200);

        // Verify only pointage logs are in response
        $responseData = $response->json();
        $logIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($pointageLog->id, $logIds);
        $this->assertNotContains($loginLog->id, $logIds);
        $this->assertNotContains($logoutLog->id, $logIds);

        // Verify all logs exist in database
        $this->assertDatabaseHasRecord('logs', ['id' => $loginLog->id, 'action' => 'login']);
        $this->assertDatabaseHasRecord('logs', ['id' => $pointageLog->id, 'action' => 'pointage_start']);
        $this->assertDatabaseHasRecord('logs', ['id' => $logoutLog->id, 'action' => 'logout']);

        LaravelLog::info('Admin filter logs by action test completed successfully');
    }

    /**
     * Test admin can filter logs by user.
     */
    public function test_admin_can_filter_logs_by_user(): void
    {
        LaravelLog::info('Testing admin can filter logs by user');

        // Create admin and employees
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);

        // Create logs for different users
        $log1 = Log::create([
            'user_id' => $employee1->id,
            'action' => 'login',
            'details' => json_encode(['ip' => '127.0.0.1'])
        ]);

        $log2 = Log::create([
            'user_id' => $employee1->id,
            'action' => 'pointage_start',
            'details' => json_encode(['site_id' => 1])
        ]);

        $log3 = Log::create([
            'user_id' => $employee2->id,
            'action' => 'login',
            'details' => json_encode(['ip' => '127.0.0.1'])
        ]);

        Sanctum::actingAs($admin);

        // Request logs filtered by user
        $response = $this->getJson("/api/admin/logs?user_id={$employee1->id}");

        // Assert successful response
        $response->assertStatus(200);

        // Verify only employee1's logs are in response
        $responseData = $response->json();
        $logIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($log1->id, $logIds);
        $this->assertContains($log2->id, $logIds);
        $this->assertNotContains($log3->id, $logIds);

        // Verify all logs exist in database
        $this->assertDatabaseHasRecord('logs', ['id' => $log1->id, 'user_id' => $employee1->id]);
        $this->assertDatabaseHasRecord('logs', ['id' => $log2->id, 'user_id' => $employee1->id]);
        $this->assertDatabaseHasRecord('logs', ['id' => $log3->id, 'user_id' => $employee2->id]);

        LaravelLog::info('Admin filter logs by user test completed successfully');
    }

    /**
     * Test admin can filter logs by date range.
     */
    public function test_admin_can_filter_logs_by_date_range(): void
    {
        LaravelLog::info('Testing admin can filter logs by date range');

        // Create admin and employee
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser();

        // Create logs with different dates
        $oldLog = Log::create([
            'user_id' => $employee->id,
            'action' => 'login',
            'details' => json_encode(['ip' => '127.0.0.1']),
            'created_at' => Carbon::now()->subWeek()
        ]);

        $recentLog = Log::create([
            'user_id' => $employee->id,
            'action' => 'pointage_start',
            'details' => json_encode(['site_id' => 1]),
            'created_at' => Carbon::now()->subDay()
        ]);

        Sanctum::actingAs($admin);

        // Request logs for last 3 days
        $fromDate = Carbon::now()->subDays(3)->toDateString();
        $toDate = Carbon::now()->toDateString();
        
        $response = $this->getJson("/api/admin/logs?from={$fromDate}&to={$toDate}");

        // Assert successful response
        $response->assertStatus(200);

        // Verify only recent log is in response
        $responseData = $response->json();
        $logIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($recentLog->id, $logIds);
        $this->assertNotContains($oldLog->id, $logIds);

        // Verify both logs exist in database
        $this->assertDatabaseHasRecord('logs', ['id' => $oldLog->id]);
        $this->assertDatabaseHasRecord('logs', ['id' => $recentLog->id]);

        LaravelLog::info('Admin filter logs by date range test completed successfully');
    }

    /**
     * Test employee cannot access admin log endpoints.
     */
    public function test_employee_cannot_access_admin_log_endpoints(): void
    {
        LaravelLog::info('Testing employee cannot access admin log endpoints');

        // Create regular employee
        $employee = $this->createTestUser();
        Sanctum::actingAs($employee);

        // Try to access admin log endpoints
        $endpoints = [
            '/api/admin/logs',
            '/api/admin/logs?action=login',
            '/api/admin/logs?user_id=1',
            '/api/admin/logs/stats'
        ];

        foreach ($endpoints as $endpoint) {
            $response = $this->getJson($endpoint);
            
            // Assert forbidden access
            $response->assertStatus(403);
        }

        LaravelLog::info('Employee log access restriction test completed successfully');
    }

    /**
     * Test log statistics for admin.
     */
    public function test_log_statistics_for_admin(): void
    {
        LaravelLog::info('Testing log statistics for admin');

        // Create admin and employees
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);

        // Create various logs
        Log::create(['user_id' => $employee1->id, 'action' => 'login', 'details' => '{}']);
        Log::create(['user_id' => $employee1->id, 'action' => 'pointage_start', 'details' => '{}']);
        Log::create(['user_id' => $employee2->id, 'action' => 'login', 'details' => '{}']);
        Log::create(['user_id' => $employee2->id, 'action' => 'pointage_end', 'details' => '{}']);
        Log::create(['user_id' => $admin->id, 'action' => 'employee_created', 'details' => '{}']);

        Sanctum::actingAs($admin);

        // Request log statistics
        $response = $this->getJson('/api/admin/logs/stats');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'total_logs',
                     'today_logs',
                     'logs_by_action' => [
                         '*' => ['action', 'count']
                     ],
                     'logs_by_user' => [
                         '*' => ['user_id', 'user_name', 'count']
                     ]
                 ]);

        // Verify statistics
        $responseData = $response->json();
        $this->assertEquals(5, $responseData['total_logs']);

        // Verify data comes from database
        $totalInDb = Log::count();
        $this->assertEquals($totalInDb, $responseData['total_logs']);

        LaravelLog::info('Log statistics test completed successfully');
    }

    /**
     * Test automatic log creation for pointage actions.
     */
    public function test_automatic_log_creation_for_pointage_actions(): void
    {
        LaravelLog::info('Testing automatic log creation for pointage actions');

        // Create employee and site
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        $this->createTestAssignment($employee, $site);
        
        Sanctum::actingAs($employee);

        // Start pointage (should create log)
        $response = $this->postJson('/api/pointages/start', [
            'site_id' => $site->id,
            'debut_latitude' => 33.8869,
            'debut_longitude' => -9.5375
        ]);

        $response->assertStatus(201);

        // Verify log is created for pointage start
        $this->assertDatabaseHas('logs', [
            'user_id' => $employee->id,
            'action' => 'pointage_start'
        ]);

        // Get the pointage and end it
        $pointage = \App\Models\Pointage::where('user_id', $employee->id)->first();
        
        $response = $this->putJson("/api/pointages/{$pointage->id}/end", [
            'fin_latitude' => 33.8870,
            'fin_longitude' => -9.5376
        ]);

        $response->assertStatus(200);

        // Verify log is created for pointage end
        $this->assertDatabaseHas('logs', [
            'user_id' => $employee->id,
            'action' => 'pointage_end'
        ]);

        // Verify both logs exist in database
        $logCount = Log::where('user_id', $employee->id)->count();
        $this->assertGreaterThanOrEqual(2, $logCount);

        LaravelLog::info('Automatic pointage log creation test completed successfully', [
            'employee_id' => $employee->id,
            'pointage_id' => $pointage->id
        ]);
    }

    /**
     * Test log details contain relevant information.
     */
    public function test_log_details_contain_relevant_information(): void
    {
        LaravelLog::info('Testing log details contain relevant information');

        // Create employee and site
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        $this->createTestAssignment($employee, $site);
        
        Sanctum::actingAs($employee);

        // Perform action that creates detailed log
        $response = $this->postJson('/api/pointages/start', [
            'site_id' => $site->id,
            'debut_latitude' => 33.8869,
            'debut_longitude' => -9.5375
        ]);

        $response->assertStatus(201);

        // Get the created log
        $log = Log::where('user_id', $employee->id)
                  ->where('action', 'pointage_start')
                  ->first();

        $this->assertNotNull($log);
        $this->assertNotNull($log->details);

        // Verify log details contain relevant information
        $details = json_decode($log->details, true);
        $this->assertArrayHasKey('site_id', $details);
        $this->assertArrayHasKey('latitude', $details);
        $this->assertArrayHasKey('longitude', $details);
        $this->assertEquals($site->id, $details['site_id']);

        LaravelLog::info('Log details test completed successfully', [
            'log_id' => $log->id,
            'details' => $log->details
        ]);
    }
}
