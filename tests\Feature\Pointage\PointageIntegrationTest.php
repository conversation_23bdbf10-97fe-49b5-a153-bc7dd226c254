<?php

namespace Tests\Feature\Pointage;

use Tests\TestCase;
use Tests\Traits\DatabaseIntegrationTrait;
use App\Models\Pointage;
use App\Models\User;
use App\Models\Site;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\Sanctum;
use Carbon\Carbon;

class PointageIntegrationTest extends TestCase
{
    use DatabaseIntegrationTrait;

    /**
     * Test employee can start pointage.
     */
    public function test_employee_can_start_pointage(): void
    {
        Log::info('Testing employee can start pointage');

        // Create employee and site
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        $this->createTestAssignment($employee, $site);
        
        Sanctum::actingAs($employee);

        $pointageData = [
            'site_id' => $site->id,
            'debut_latitude' => 33.8869,
            'debut_longitude' => -9.5375
        ];

        // Start pointage
        $response = $this->postJson('/api/pointages/start', $pointageData);

        // Assert successful start
        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'id', 'user_id', 'site_id', 'debut_pointage', 
                     'debut_latitude', 'debut_longitude', 'created_at', 'updated_at'
                 ])
                 ->assertJson([
                     'user_id' => $employee->id,
                     'site_id' => $site->id,
                     'debut_latitude' => '33.88690000',
                     'debut_longitude' => '-9.53750000'
                 ]);

        // Verify pointage is stored in database
        $this->assertDatabaseHasRecord('pointages', [
            'user_id' => $employee->id,
            'site_id' => $site->id,
            'debut_latitude' => 33.8869,
            'debut_longitude' => -9.5375
        ]);

        // Verify fin_pointage is null (not finished yet)
        $pointage = Pointage::where('user_id', $employee->id)->first();
        $this->assertNull($pointage->fin_pointage);
        $this->assertNull($pointage->duree);

        Log::info('Employee start pointage test completed successfully', [
            'employee_id' => $employee->id,
            'site_id' => $site->id,
            'pointage_id' => $pointage->id
        ]);
    }

    /**
     * Test employee can end pointage.
     */
    public function test_employee_can_end_pointage(): void
    {
        Log::info('Testing employee can end pointage');

        // Create employee, site and active pointage
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        $this->createTestAssignment($employee, $site);
        
        $pointage = $this->createTestPointage($employee, $site, [
            'debut_pointage' => Carbon::now()->subHours(2)
        ]);
        
        Sanctum::actingAs($employee);

        $endData = [
            'fin_latitude' => 33.8870,
            'fin_longitude' => -9.5376
        ];

        // End pointage
        $response = $this->putJson("/api/pointages/{$pointage->id}/end", $endData);

        // Assert successful end
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'id', 'user_id', 'site_id', 'debut_pointage', 'fin_pointage',
                     'duree', 'fin_latitude', 'fin_longitude'
                 ])
                 ->assertJson([
                     'id' => $pointage->id,
                     'user_id' => $employee->id,
                     'site_id' => $site->id,
                     'fin_latitude' => '33.88700000',
                     'fin_longitude' => '-9.53760000'
                 ]);

        // Verify pointage is updated in database
        $this->assertDatabaseHasRecord('pointages', [
            'id' => $pointage->id,
            'user_id' => $employee->id,
            'site_id' => $site->id,
            'fin_latitude' => 33.8870,
            'fin_longitude' => -9.5376
        ]);

        // Verify fin_pointage and duree are set
        $updatedPointage = Pointage::find($pointage->id);
        $this->assertNotNull($updatedPointage->fin_pointage);
        $this->assertNotNull($updatedPointage->duree);

        Log::info('Employee end pointage test completed successfully', [
            'employee_id' => $employee->id,
            'pointage_id' => $pointage->id
        ]);
    }

    /**
     * Test employee can view their pointage history.
     */
    public function test_employee_can_view_pointage_history(): void
    {
        Log::info('Testing employee can view pointage history');

        // Create employee and sites
        $employee = $this->createTestUser();
        $site1 = $this->createTestSite(['name' => 'Site 1']);
        $site2 = $this->createTestSite(['name' => 'Site 2']);
        
        $this->createTestAssignment($employee, $site1);
        $this->createTestAssignment($employee, $site2);

        // Create multiple pointages
        $pointage1 = $this->createTestPointage($employee, $site1, [
            'debut_pointage' => Carbon::now()->subDays(2),
            'fin_pointage' => Carbon::now()->subDays(2)->addHours(8),
            'duree' => '08:00:00'
        ]);
        
        $pointage2 = $this->createTestPointage($employee, $site2, [
            'debut_pointage' => Carbon::now()->subDay(),
            'fin_pointage' => Carbon::now()->subDay()->addHours(6),
            'duree' => '06:00:00'
        ]);

        Sanctum::actingAs($employee);

        // Request pointage history
        $response = $this->getJson('/api/pointages/history');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id', 'site_id', 'debut_pointage', 'fin_pointage',
                             'duree', 'site' => ['id', 'name']
                         ]
                     ]
                 ]);

        // Verify pointages are in response
        $responseData = $response->json();
        $pointageIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($pointage1->id, $pointageIds);
        $this->assertContains($pointage2->id, $pointageIds);

        // Verify data comes from database
        $this->assertDatabaseHasRecord('pointages', ['id' => $pointage1->id]);
        $this->assertDatabaseHasRecord('pointages', ['id' => $pointage2->id]);

        Log::info('Employee view pointage history test completed successfully', [
            'employee_id' => $employee->id
        ]);
    }

    /**
     * Test admin can view all pointages.
     */
    public function test_admin_can_view_all_pointages(): void
    {
        Log::info('Testing admin can view all pointages');

        // Create admin, employees and sites
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);
        $site = $this->createTestSite();
        
        $this->createTestAssignment($employee1, $site);
        $this->createTestAssignment($employee2, $site);

        // Create pointages for different employees
        $pointage1 = $this->createTestPointage($employee1, $site);
        $pointage2 = $this->createTestPointage($employee2, $site);

        Sanctum::actingAs($admin);

        // Request all pointages
        $response = $this->getJson('/api/admin/pointages');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id', 'user_id', 'site_id', 'debut_pointage',
                             'user' => ['id', 'name'],
                             'site' => ['id', 'name']
                         ]
                     ]
                 ]);

        // Verify all pointages are in response
        $responseData = $response->json();
        $pointageIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($pointage1->id, $pointageIds);
        $this->assertContains($pointage2->id, $pointageIds);

        // Verify data comes from database
        $this->assertDatabaseHasRecord('pointages', ['id' => $pointage1->id, 'user_id' => $employee1->id]);
        $this->assertDatabaseHasRecord('pointages', ['id' => $pointage2->id, 'user_id' => $employee2->id]);

        Log::info('Admin view all pointages test completed successfully');
    }

    /**
     * Test employee cannot start pointage at unassigned site.
     */
    public function test_employee_cannot_start_pointage_at_unassigned_site(): void
    {
        Log::info('Testing employee cannot start pointage at unassigned site');

        // Create employee and site without assignment
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        
        Sanctum::actingAs($employee);

        $pointageData = [
            'site_id' => $site->id,
            'debut_latitude' => 33.8869,
            'debut_longitude' => -9.5375
        ];

        // Try to start pointage at unassigned site
        $response = $this->postJson('/api/pointages/start', $pointageData);

        // Assert forbidden
        $response->assertStatus(403)
                 ->assertJson([
                     'message' => 'You are not assigned to this site'
                 ]);

        // Verify no pointage is created in database
        $this->assertDatabaseMissingRecord('pointages', [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        Log::info('Employee unassigned site pointage test completed successfully');
    }

    /**
     * Test employee cannot start multiple active pointages.
     */
    public function test_employee_cannot_start_multiple_active_pointages(): void
    {
        Log::info('Testing employee cannot start multiple active pointages');

        // Create employee and sites
        $employee = $this->createTestUser();
        $site1 = $this->createTestSite(['name' => 'Site 1']);
        $site2 = $this->createTestSite(['name' => 'Site 2']);
        
        $this->createTestAssignment($employee, $site1);
        $this->createTestAssignment($employee, $site2);

        // Create active pointage
        $activePointage = $this->createTestPointage($employee, $site1);
        
        Sanctum::actingAs($employee);

        $pointageData = [
            'site_id' => $site2->id,
            'debut_latitude' => 33.8869,
            'debut_longitude' => -9.5375
        ];

        // Try to start another pointage while one is active
        $response = $this->postJson('/api/pointages/start', $pointageData);

        // Assert conflict
        $response->assertStatus(409)
                 ->assertJson([
                     'message' => 'You have an active pointage. Please end it first.'
                 ]);

        // Verify only one pointage exists for this user
        $pointageCount = Pointage::where('user_id', $employee->id)->count();
        $this->assertEquals(1, $pointageCount);

        Log::info('Employee multiple active pointages test completed successfully');
    }

    /**
     * Test pointage duration calculation.
     */
    public function test_pointage_duration_calculation(): void
    {
        Log::info('Testing pointage duration calculation');

        // Create employee and site
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        $this->createTestAssignment($employee, $site);
        
        $startTime = Carbon::now()->subHours(3)->subMinutes(30);
        $pointage = $this->createTestPointage($employee, $site, [
            'debut_pointage' => $startTime
        ]);
        
        Sanctum::actingAs($employee);

        $endData = [
            'fin_latitude' => 33.8870,
            'fin_longitude' => -9.5376
        ];

        // End pointage
        $response = $this->putJson("/api/pointages/{$pointage->id}/end", $endData);

        // Assert successful end
        $response->assertStatus(200);

        // Verify duration is calculated correctly in database
        $updatedPointage = Pointage::find($pointage->id);
        $this->assertNotNull($updatedPointage->duree);
        
        // Duration should be approximately 3.5 hours
        $durationParts = explode(':', $updatedPointage->duree);
        $hours = (int)$durationParts[0];
        $minutes = (int)$durationParts[1];
        
        $this->assertEquals(3, $hours);
        $this->assertGreaterThanOrEqual(25, $minutes);
        $this->assertLessThanOrEqual(35, $minutes);

        Log::info('Pointage duration calculation test completed successfully', [
            'pointage_id' => $pointage->id,
            'duration' => $updatedPointage->duree
        ]);
    }

    /**
     * Test employee can view current active pointage.
     */
    public function test_employee_can_view_current_active_pointage(): void
    {
        Log::info('Testing employee can view current active pointage');

        // Create employee, site and active pointage
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        $this->createTestAssignment($employee, $site);
        
        $pointage = $this->createTestPointage($employee, $site);
        
        Sanctum::actingAs($employee);

        // Request current active pointage
        $response = $this->getJson('/api/pointages/current');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJson([
                     'id' => $pointage->id,
                     'user_id' => $employee->id,
                     'site_id' => $site->id,
                     'fin_pointage' => null
                 ]);

        // Verify data comes from database
        $this->assertDatabaseHasRecord('pointages', [
            'id' => $pointage->id,
            'user_id' => $employee->id,
            'fin_pointage' => null
        ]);

        Log::info('Employee view current active pointage test completed successfully', [
            'employee_id' => $employee->id,
            'pointage_id' => $pointage->id
        ]);
    }
}
