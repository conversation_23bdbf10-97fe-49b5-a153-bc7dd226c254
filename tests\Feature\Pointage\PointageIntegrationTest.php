<?php

namespace Tests\Feature\Pointage;

use Tests\TestCase;
use Tests\Traits\DatabaseTestCase;
use Tests\Traits\AuthenticationHelpers;
use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Assignment;
use Carbon\Carbon;

class PointageIntegrationTest extends TestCase
{
    use DatabaseTestCase, AuthenticationHelpers;

    /**
     * Test de début de pointage
     */
    public function test_user_can_start_pointage(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create();
        
        // Assigner l'utilisateur au site
        Assignment::factory()->forUserAndSite($user, $site)->create();

        $pointageData = [
            'site_id' => $site->id,
            'latitude' => $site->latitude + 0.0001, // Proche du site
            'longitude' => $site->longitude + 0.0001
        ];

        $response = $this->authenticatedPost('/api/pointages/start', $pointageData, $user);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'pointage' => [
                            'id',
                            'user_id',
                            'site_id',
                            'debut_pointage',
                            'debut_latitude',
                            'debut_longitude'
                        ]
                    ],
                    'message'
                ]);

        $this->assertDatabaseHasRecord('pointages', [
            'user_id' => $user->id,
            'site_id' => $site->id,
            'fin_pointage' => null
        ]);
    }

    /**
     * Test qu'un utilisateur ne peut pas commencer un pointage s'il en a déjà un en cours
     */
    public function test_user_cannot_start_pointage_if_already_active(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create();
        
        Assignment::factory()->forUserAndSite($user, $site)->create();
        Pointage::factory()->forUser($user)->forSite($site)->enCours()->create();

        $pointageData = [
            'site_id' => $site->id,
            'latitude' => $site->latitude,
            'longitude' => $site->longitude
        ];

        $response = $this->authenticatedPost('/api/pointages/start', $pointageData, $user);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Un pointage est déjà en cours'
                ]);
    }

    /**
     * Test de fin de pointage
     */
    public function test_user_can_end_pointage(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create();
        
        $pointage = Pointage::factory()->forUser($user)->forSite($site)->enCours()->create();

        $endData = [
            'latitude' => $site->latitude + 0.0001,
            'longitude' => $site->longitude + 0.0001
        ];

        $response = $this->authenticatedPost("/api/pointages/{$pointage->id}/end", $endData, $user);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'pointage' => [
                            'id',
                            'fin_pointage',
                            'duree',
                            'fin_latitude',
                            'fin_longitude'
                        ]
                    ],
                    'message'
                ]);

        $pointage->refresh();
        $this->assertNotNull($pointage->fin_pointage);
        $this->assertNotNull($pointage->duree);
        $this->assertEquals($endData['latitude'], (float)$pointage->fin_latitude);
        $this->assertEquals($endData['longitude'], (float)$pointage->fin_longitude);
    }

    /**
     * Test de récupération des pointages de l'utilisateur
     */
    public function test_user_can_get_own_pointages(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create();
        
        Pointage::factory()->forUser($user)->forSite($site)->count(3)->create();

        $response = $this->authenticatedGet('/api/pointages', $user);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'site' => [
                                'id',
                                'name'
                            ],
                            'debut_pointage',
                            'fin_pointage',
                            'duree'
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('data'));
    }

    /**
     * Test de récupération du pointage actuel
     */
    public function test_user_can_get_current_pointage(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create();
        
        $pointage = Pointage::factory()->forUser($user)->forSite($site)->enCours()->create();

        $response = $this->authenticatedGet('/api/pointages/current', $user);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'pointage' => [
                            'id' => $pointage->id,
                            'site_id' => $site->id
                        ]
                    ]
                ]);
    }

    /**
     * Test quand il n'y a pas de pointage en cours
     */
    public function test_user_gets_null_when_no_current_pointage(): void
    {
        $user = $this->createTestUser();

        $response = $this->authenticatedGet('/api/pointages/current', $user);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'pointage' => null
                    ]
                ]);
    }

    /**
     * Test de filtrage des pointages par date
     */
    public function test_can_filter_pointages_by_date(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create();

        // Créer des pointages à différentes dates
        Pointage::factory()->forUser($user)->forSite($site)->create([
            'debut_pointage' => Carbon::today()
        ]);
        Pointage::factory()->forUser($user)->forSite($site)->create([
            'debut_pointage' => Carbon::yesterday()
        ]);
        Pointage::factory()->forUser($user)->forSite($site)->create([
            'debut_pointage' => Carbon::today()->subDays(2)
        ]);

        $today = Carbon::today()->format('Y-m-d');
        $response = $this->authenticatedGet("/api/pointages?date={$today}", $user);

        $response->assertStatus(200);
        $pointages = $response->json('data');
        $this->assertCount(1, $pointages);
    }

    /**
     * Test de validation de la distance pour commencer un pointage
     */
    public function test_pointage_start_validates_distance_from_site(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create([
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ]);
        
        Assignment::factory()->forUserAndSite($user, $site)->create();

        // Coordonnées trop éloignées (plus de 100m)
        $pointageData = [
            'site_id' => $site->id,
            'latitude' => 48.8600, // ~400m de différence
            'longitude' => 2.3600
        ];

        $response = $this->authenticatedPost('/api/pointages/start', $pointageData, $user);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Vous êtes trop éloigné du site pour pointer'
                ]);
    }

    /**
     * Test qu'un utilisateur ne peut pointer que sur ses sites assignés
     */
    public function test_user_can_only_point_on_assigned_sites(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create();
        // Pas d'assignment créé

        $pointageData = [
            'site_id' => $site->id,
            'latitude' => $site->latitude,
            'longitude' => $site->longitude
        ];

        $response = $this->authenticatedPost('/api/pointages/start', $pointageData, $user);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Vous n\'êtes pas assigné à ce site'
                ]);
    }

    /**
     * Test de calcul automatique de la durée
     */
    public function test_pointage_duration_is_calculated_automatically(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create();
        
        $startTime = Carbon::now()->subHours(2);
        $pointage = Pointage::factory()->forUser($user)->forSite($site)->create([
            'debut_pointage' => $startTime,
            'fin_pointage' => null,
            'duree' => null
        ]);

        $endData = [
            'latitude' => $site->latitude,
            'longitude' => $site->longitude
        ];

        $response = $this->authenticatedPost("/api/pointages/{$pointage->id}/end", $endData, $user);

        $response->assertStatus(200);
        
        $pointage->refresh();
        $this->assertNotNull($pointage->duree);
        
        // Vérifier que la durée est d'environ 2 heures
        $duration = Carbon::parse($pointage->duree);
        $this->assertEquals(2, $duration->hour);
    }

    /**
     * Test de récupération des statistiques de pointage
     */
    public function test_user_can_get_pointage_statistics(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create();

        // Créer plusieurs pointages terminés
        Pointage::factory()->forUser($user)->forSite($site)->termine()->count(5)->create();
        Pointage::factory()->forUser($user)->forSite($site)->enCours()->create();

        $response = $this->authenticatedGet('/api/pointages/statistics', $user);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_pointages',
                        'pointages_en_cours',
                        'temps_total_travaille',
                        'moyenne_duree_pointage'
                    ]
                ]);

        $stats = $response->json('data');
        $this->assertEquals(6, $stats['total_pointages']);
        $this->assertEquals(1, $stats['pointages_en_cours']);
    }

    /**
     * Test qu'un admin peut voir tous les pointages
     */
    public function test_admin_can_see_all_pointages(): void
    {
        $admin = $this->createTestAdmin();
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $site = Site::factory()->create();

        Pointage::factory()->forUser($user1)->forSite($site)->count(2)->create();
        Pointage::factory()->forUser($user2)->forSite($site)->count(3)->create();

        $response = $this->authenticatedGet('/api/admin/pointages', $admin);

        $response->assertStatus(200);
        $pointages = $response->json('data');
        $this->assertCount(5, $pointages);
    }

    /**
     * Test qu'un employé ne peut pas voir les pointages des autres
     */
    public function test_employee_cannot_see_other_pointages(): void
    {
        $employee = $this->createTestUser();

        $response = $this->authenticatedGet('/api/admin/pointages', $employee);

        $response->assertStatus(403);
    }
}
