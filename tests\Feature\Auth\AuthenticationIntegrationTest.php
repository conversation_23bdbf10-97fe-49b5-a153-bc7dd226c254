<?php

namespace Tests\Feature\Auth;

use Tests\TestCase;
use Tests\Traits\DatabaseIntegrationTrait;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\Sanctum;

class AuthenticationIntegrationTest extends TestCase
{
    use DatabaseIntegrationTrait;

    /**
     * Test user registration with database integration.
     */
    public function test_user_can_register_and_data_is_stored_in_database(): void
    {
        Log::info('Testing user registration with database integration');

        $userData = [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'employee'
        ];

        // Simulate API registration request
        $response = $this->postJson('/api/register', $userData);

        // Assert response structure (assuming API returns user data)
        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'user' => ['id', 'name', 'email', 'role'],
                     'token'
                 ]);

        // Assert user is stored in database
        $this->assertDatabaseHasRecord('users', [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'role' => 'employee'
        ]);

        // Verify password is hashed
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertTrue(Hash::check('password123', $user->password));
        $this->assertNotEquals('password123', $user->password);

        Log::info('User registration test completed successfully', ['user_id' => $user->id]);
    }

    /**
     * Test user login with database integration.
     */
    public function test_user_can_login_with_valid_credentials(): void
    {
        Log::info('Testing user login with database integration');

        // Create user in database
        $user = $this->createTestUser([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123')
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        // Simulate API login request
        $response = $this->postJson('/api/login', $loginData);

        // Assert successful login
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'user' => ['id', 'name', 'email', 'role'],
                     'token'
                 ]);

        // Verify user data in response
        $responseData = $response->json();
        $this->assertEquals($user->id, $responseData['user']['id']);
        $this->assertEquals($user->email, $responseData['user']['email']);
        $this->assertNotEmpty($responseData['token']);

        // Verify token is created in database
        $this->assertDatabaseHas('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'tokenable_type' => User::class
        ]);

        Log::info('User login test completed successfully', ['user_id' => $user->id]);
    }

    /**
     * Test user login with invalid credentials.
     */
    public function test_user_cannot_login_with_invalid_credentials(): void
    {
        Log::info('Testing user login with invalid credentials');

        // Create user in database
        $user = $this->createTestUser([
            'email' => '<EMAIL>',
            'password' => Hash::make('correct_password')
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'wrong_password'
        ];

        // Simulate API login request with wrong password
        $response = $this->postJson('/api/login', $loginData);

        // Assert login failure
        $response->assertStatus(401)
                 ->assertJson([
                     'message' => 'Invalid credentials'
                 ]);

        // Verify no token is created
        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'tokenable_type' => User::class
        ]);

        Log::info('Invalid credentials test completed successfully');
    }

    /**
     * Test user logout with database integration.
     */
    public function test_user_can_logout_and_token_is_revoked(): void
    {
        Log::info('Testing user logout with database integration');

        // Create user and authenticate
        $user = $this->createTestUser();
        Sanctum::actingAs($user);

        // Create a token for the user
        $token = $user->createToken('test-token');

        // Verify token exists in database
        $this->assertDatabaseHas('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'tokenable_type' => User::class,
            'name' => 'test-token'
        ]);

        // Simulate API logout request
        $response = $this->postJson('/api/logout');

        // Assert successful logout
        $response->assertStatus(200)
                 ->assertJson([
                     'message' => 'Logged out successfully'
                 ]);

        // Verify token is revoked/deleted from database
        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'tokenable_type' => User::class,
            'name' => 'test-token'
        ]);

        Log::info('User logout test completed successfully', ['user_id' => $user->id]);
    }

    /**
     * Test getting authenticated user profile.
     */
    public function test_authenticated_user_can_get_profile(): void
    {
        Log::info('Testing authenticated user profile retrieval');

        // Create and authenticate user
        $user = $this->createTestUser([
            'name' => 'Profile User',
            'email' => '<EMAIL>'
        ]);
        Sanctum::actingAs($user);

        // Simulate API profile request
        $response = $this->getJson('/api/user');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJson([
                     'id' => $user->id,
                     'name' => 'Profile User',
                     'email' => '<EMAIL>',
                     'role' => 'employee'
                 ]);

        // Verify user still exists in database
        $this->assertDatabaseHasRecord('users', [
            'id' => $user->id,
            'email' => '<EMAIL>'
        ]);

        Log::info('User profile test completed successfully', ['user_id' => $user->id]);
    }

    /**
     * Test unauthenticated user cannot access protected routes.
     */
    public function test_unauthenticated_user_cannot_access_protected_routes(): void
    {
        Log::info('Testing unauthenticated access to protected routes');

        // Try to access protected route without authentication
        $response = $this->getJson('/api/user');

        // Assert unauthorized response
        $response->assertStatus(401);

        Log::info('Unauthenticated access test completed successfully');
    }

    /**
     * Test user registration with duplicate email.
     */
    public function test_user_cannot_register_with_duplicate_email(): void
    {
        Log::info('Testing user registration with duplicate email');

        // Create existing user
        $existingUser = $this->createTestUser(['email' => '<EMAIL>']);

        $userData = [
            'name' => 'Another User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123'
        ];

        // Try to register with same email
        $response = $this->postJson('/api/register', $userData);

        // Assert validation error
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['email']);

        // Verify only one user exists with this email
        $userCount = User::where('email', '<EMAIL>')->count();
        $this->assertEquals(1, $userCount);

        Log::info('Duplicate email registration test completed successfully');
    }
}
