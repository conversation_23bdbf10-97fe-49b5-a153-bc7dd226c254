<?php

namespace Tests\Feature\Auth;

use Tests\TestCase;
use Tests\Traits\DatabaseTestCase;
use Tests\Traits\AuthenticationHelpers;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Lara<PERSON>\Sanctum\PersonalAccessToken;

class AuthenticationIntegrationTest extends TestCase
{
    use DatabaseTestCase, AuthenticationHelpers;

    /**
     * Test d'inscription d'un nouvel utilisateur
     */
    public function test_user_can_register(): void
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'employee'
        ];

        $response = $this->withHeaders($this->getJsonHeaders())
                         ->postJson('/api/auth/register', $userData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'role'
                        ],
                        'token'
                    ],
                    'message'
                ]);

        $this->assertDatabaseHasRecord('users', [
            'email' => '<EMAIL>',
            'role' => 'employee'
        ]);
    }

    /**
     * Test de connexion avec des identifiants valides
     */
    public function test_user_can_login_with_valid_credentials(): void
    {
        $user = $this->createTestUser([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123')
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->withHeaders($this->getJsonHeaders())
                         ->postJson('/api/auth/login', $loginData);

        $this->assertAuthenticationResponse($response, true);
        
        // Vérifier qu'un token a été créé
        $this->assertDatabaseHasRecord('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'tokenable_type' => User::class
        ]);
    }

    /**
     * Test de connexion avec des identifiants invalides
     */
    public function test_user_cannot_login_with_invalid_credentials(): void
    {
        $this->createTestUser([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123')
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ];

        $response = $this->withHeaders($this->getJsonHeaders())
                         ->postJson('/api/auth/login', $loginData);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Identifiants invalides'
                ]);
    }

    /**
     * Test de déconnexion
     */
    public function test_user_can_logout(): void
    {
        $user = $this->createTestUser();
        $token = $this->createAuthToken($user);

        $response = $this->withHeaders($this->getAuthHeaders($token))
                         ->postJson('/api/auth/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Déconnexion réussie'
                ]);

        // Vérifier que le token a été supprimé
        $this->assertDatabaseMissingRecord('personal_access_tokens', [
            'tokenable_id' => $user->id,
            'token' => hash('sha256', explode('|', $token)[1])
        ]);
    }

    /**
     * Test de récupération du profil utilisateur
     */
    public function test_user_can_get_profile(): void
    {
        $user = $this->createTestUser([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'role' => 'employee'
        ]);

        $response = $this->authenticatedGet('/api/auth/profile', $user);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'role',
                            'created_at',
                            'updated_at'
                        ]
                    ]
                ])
                ->assertJson([
                    'data' => [
                        'user' => [
                            'name' => 'John Doe',
                            'email' => '<EMAIL>',
                            'role' => 'employee'
                        ]
                    ]
                ]);
    }

    /**
     * Test de mise à jour du profil
     */
    public function test_user_can_update_profile(): void
    {
        $user = $this->createTestUser();

        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>'
        ];

        $response = $this->authenticatedPut('/api/auth/profile', $updateData, $user);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'user' => [
                            'name' => 'Updated Name',
                            'email' => '<EMAIL>'
                        ]
                    ]
                ]);

        $this->assertDatabaseHasRecord('users', [
            'id' => $user->id,
            'name' => 'Updated Name',
            'email' => '<EMAIL>'
        ]);
    }

    /**
     * Test de changement de mot de passe
     */
    public function test_user_can_change_password(): void
    {
        $user = $this->createTestUser([
            'password' => Hash::make('oldpassword')
        ]);

        $passwordData = [
            'current_password' => 'oldpassword',
            'new_password' => 'newpassword123',
            'new_password_confirmation' => 'newpassword123'
        ];

        $response = $this->authenticatedPut('/api/auth/change-password', $passwordData, $user);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Mot de passe modifié avec succès'
                ]);

        // Vérifier que le nouveau mot de passe fonctionne
        $user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $user->password));
    }

    /**
     * Test de validation des données d'inscription
     */
    public function test_registration_validation(): void
    {
        $invalidData = [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123',
            'password_confirmation' => '456'
        ];

        $response = $this->withHeaders($this->getJsonHeaders())
                         ->postJson('/api/auth/register', $invalidData);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'email', 'password']);
    }

    /**
     * Test d'accès aux routes protégées sans authentification
     */
    public function test_protected_routes_require_authentication(): void
    {
        $protectedRoutes = [
            ['GET', '/api/auth/profile'],
            ['PUT', '/api/auth/profile'],
            ['PUT', '/api/auth/change-password'],
            ['POST', '/api/auth/logout'],
        ];

        foreach ($protectedRoutes as [$method, $uri]) {
            $this->assertRequiresAuthentication($method, $uri);
        }
    }

    /**
     * Test de gestion des tokens multiples
     */
    public function test_user_can_have_multiple_tokens(): void
    {
        $user = $this->createTestUser();

        $token1 = $this->createAuthToken($user, 'device-1');
        $token2 = $this->createAuthToken($user, 'device-2');

        $this->assertNotEquals($token1, $token2);

        // Vérifier que les deux tokens fonctionnent
        $response1 = $this->withHeaders($this->getAuthHeaders($token1))
                          ->getJson('/api/auth/profile');
        $response1->assertStatus(200);

        $response2 = $this->withHeaders($this->getAuthHeaders($token2))
                          ->getJson('/api/auth/profile');
        $response2->assertStatus(200);

        // Vérifier qu'il y a 2 tokens en base
        $this->assertEquals(2, PersonalAccessToken::where('tokenable_id', $user->id)->count());
    }

    /**
     * Test de révocation de tous les tokens
     */
    public function test_user_can_revoke_all_tokens(): void
    {
        $user = $this->createTestUser();
        
        $this->createAuthToken($user, 'device-1');
        $this->createAuthToken($user, 'device-2');
        $token3 = $this->createAuthToken($user, 'device-3');

        $response = $this->withHeaders($this->getAuthHeaders($token3))
                         ->postJson('/api/auth/logout-all');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Déconnexion de tous les appareils réussie'
                ]);

        // Vérifier que tous les tokens ont été supprimés
        $this->assertEquals(0, PersonalAccessToken::where('tokenable_id', $user->id)->count());
    }
}
