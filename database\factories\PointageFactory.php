<?php

namespace Database\Factories;

use App\Models\Pointage;
use App\Models\User;
use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Pointage>
 */
class PointageFactory extends Factory
{
    protected $model = Pointage::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $debutPointage = $this->faker->dateTimeBetween('-1 week', 'now');
        $finPointage = $this->faker->optional(0.8)->dateTimeBetween($debutPointage, 'now');
        
        // Calculer la durée si fin_pointage existe
        $duree = null;
        if ($finPointage) {
            $debut = Carbon::parse($debutPointage);
            $fin = Carbon::parse($finPointage);
            $duree = $debut->diff($fin)->format('%H:%I:%S');
        }

        return [
            'user_id' => User::factory(),
            'site_id' => Site::factory(),
            'debut_pointage' => $debutPointage,
            'fin_pointage' => $finPointage,
            'duree' => $duree,
            'debut_latitude' => $this->faker->latitude(45.0, 49.0),
            'debut_longitude' => $this->faker->longitude(-5.0, 8.0),
            'fin_latitude' => $finPointage ? $this->faker->latitude(45.0, 49.0) : null,
            'fin_longitude' => $finPointage ? $this->faker->longitude(-5.0, 8.0) : null,
        ];
    }

    /**
     * Pointage en cours (pas encore terminé)
     */
    public function enCours(): static
    {
        return $this->state(fn (array $attributes) => [
            'debut_pointage' => now()->subHours(2),
            'fin_pointage' => null,
            'duree' => null,
            'fin_latitude' => null,
            'fin_longitude' => null,
        ]);
    }

    /**
     * Pointage terminé
     */
    public function termine(): static
    {
        $debut = now()->subHours(8);
        $fin = $debut->copy()->addHours(7)->addMinutes(30);
        
        return $this->state(fn (array $attributes) => [
            'debut_pointage' => $debut,
            'fin_pointage' => $fin,
            'duree' => '07:30:00',
            'fin_latitude' => $this->faker->latitude(45.0, 49.0),
            'fin_longitude' => $this->faker->longitude(-5.0, 8.0),
        ]);
    }

    /**
     * Pointage pour un utilisateur spécifique
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Pointage pour un site spécifique
     */
    public function forSite(Site $site): static
    {
        return $this->state(fn (array $attributes) => [
            'site_id' => $site->id,
            'debut_latitude' => $site->latitude + $this->faker->randomFloat(6, -0.001, 0.001),
            'debut_longitude' => $site->longitude + $this->faker->randomFloat(6, -0.001, 0.001),
        ]);
    }

    /**
     * Pointage d'aujourd'hui
     */
    public function aujourdhui(): static
    {
        $debut = today()->addHours(8);
        
        return $this->state(fn (array $attributes) => [
            'debut_pointage' => $debut,
            'fin_pointage' => null,
            'duree' => null,
        ]);
    }

    /**
     * Pointage d'hier
     */
    public function hier(): static
    {
        $debut = yesterday()->addHours(8);
        $fin = $debut->copy()->addHours(8);
        
        return $this->state(fn (array $attributes) => [
            'debut_pointage' => $debut,
            'fin_pointage' => $fin,
            'duree' => '08:00:00',
        ]);
    }

    /**
     * Pointage avec durée spécifique
     */
    public function withDuration(int $hours, int $minutes = 0): static
    {
        $debut = now()->subHours($hours)->subMinutes($minutes);
        $fin = now();
        $duree = sprintf('%02d:%02d:00', $hours, $minutes);
        
        return $this->state(fn (array $attributes) => [
            'debut_pointage' => $debut,
            'fin_pointage' => $fin,
            'duree' => $duree,
        ]);
    }
}
