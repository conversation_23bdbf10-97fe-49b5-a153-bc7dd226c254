# Tests d'Intégration ClockIn

Ce dossier contient une suite complète de tests d'intégration pour l'application ClockIn, conçue pour tester tous les endpoints API en interaction avec la base de données MySQL réelle.

## 📋 Vue d'ensemble

Les tests d'intégration couvrent :
- **Authentification** : Inscription, connexion, gestion des tokens Sanctum
- **Gestion des sites** : CRUD complet des chantiers
- **Gestion des employés** : CRUD des utilisateurs
- **Pointages** : Début/fin de pointage avec calcul automatique de durée
- **Assignments** : Affectation des employés aux sites
- **Vérifications** : Vérifications de localisation GPS
- **Workflow complet** : Tests end-to-end de l'ensemble du processus

## 🗄️ Configuration de la Base de Données

### Prérequis
- MySQL 8.0+
- PHP 8.2+
- Laravel 11
- Extensions PHP : pdo, pdo_mysql, mbstring, openssl, json

### Configuration
Les tests utilisent une base de données MySQL séparée configurée dans `phpunit.xml` :

```xml
<env name="DB_CONNECTION" value="mysql"/>
<env name="DB_HOST" value="127.0.0.1"/>
<env name="DB_PORT" value="3306"/>
<env name="DB_DATABASE" value="clockin_db_test"/>
<env name="DB_USERNAME" value="root"/>
<env name="DB_PASSWORD" value=""/>
```

### Accès phpMyAdmin
La base de données est accessible via phpMyAdmin à l'adresse :
`http://localhost:8080/phpmyadmin/index.php?route=/database/structure&db=clockin_db_test`

## 🚀 Exécution des Tests

### Script automatisé (Recommandé)
```bash
# Exécution complète
php run-integration-tests.php

# Préparer uniquement la base de données
php run-integration-tests.php --setup

# Nettoyer la base de données
php run-integration-tests.php --cleanup

# Valider la structure
php run-integration-tests.php --validate

# Générer un rapport de couverture
php run-integration-tests.php --coverage
```

### Exécution manuelle
```bash
# Tous les tests d'intégration
php artisan test tests/Feature/Integration --env=testing

# Tests spécifiques
php artisan test tests/Feature/Auth/AuthenticationIntegrationTest.php --env=testing
php artisan test tests/Feature/Site/SiteIntegrationTest.php --env=testing
php artisan test tests/Feature/Pointage/PointageIntegrationTest.php --env=testing
```

## 📁 Structure des Tests

```
tests/
├── Feature/
│   ├── Auth/
│   │   └── AuthenticationIntegrationTest.php    # Tests d'authentification
│   ├── Site/
│   │   └── SiteIntegrationTest.php              # Tests des chantiers
│   ├── Employee/
│   │   └── EmployeeIntegrationTest.php          # Tests des employés
│   ├── Pointage/
│   │   └── PointageIntegrationTest.php          # Tests des pointages
│   ├── Assignment/
│   │   └── AssignmentIntegrationTest.php        # Tests des assignments
│   ├── Verification/
│   │   └── VerificationIntegrationTest.php      # Tests des vérifications
│   └── Integration/
│       ├── DatabaseIntegrationTest.php         # Tests de base de données
│       └── CompleteWorkflowIntegrationTest.php # Tests end-to-end
├── Traits/
│   ├── DatabaseTestCase.php                    # Gestion des transactions DB
│   └── AuthenticationHelpers.php               # Helpers d'authentification
├── TestHelper.php                              # Utilitaires de test
└── README.md                                   # Cette documentation
```

## 🔧 Traits et Helpers

### DatabaseTestCase
Gère les transactions de base de données pour isoler les tests :
```php
use Tests\Traits\DatabaseTestCase;

class MyTest extends TestCase
{
    use DatabaseTestCase;
    // Chaque test s'exécute dans une transaction isolée
}
```

### AuthenticationHelpers
Fournit des méthodes pour l'authentification dans les tests :
```php
use Tests\Traits\AuthenticationHelpers;

// Créer et authentifier un utilisateur
$user = $this->authenticateUser();

// Effectuer une requête authentifiée
$response = $this->authenticatedGet('/api/profile', $user);
```

## 🏭 Factories

Les factories génèrent des données de test cohérentes :

```php
// Utilisateurs
User::factory()->create(['role' => 'admin']);
User::factory()->create(['role' => 'employee']);

// Sites
Site::factory()->paris()->create();
Site::factory()->withCoordinates(48.8566, 2.3522)->create();

// Pointages
Pointage::factory()->enCours()->create();
Pointage::factory()->termine()->create();
```

## 📊 Types de Tests

### Tests de Base de Données
- Vérification de l'existence des tables
- Test des contraintes de clés étrangères
- Test des suppressions en cascade
- Tests de performance

### Tests d'Authentification
- Inscription/connexion
- Gestion des tokens Sanctum
- Changement de mot de passe
- Déconnexion multiple

### Tests CRUD
- Création, lecture, mise à jour, suppression
- Validation des données
- Contrôles d'autorisation
- Gestion des erreurs

### Tests de Workflow
- Processus complet employé
- Processus d'administration
- Gestion des cas d'erreur
- Tests de performance

## 🔍 Validation des Données

Les tests vérifient :
- **Intégrité référentielle** : Contraintes FK respectées
- **Validation métier** : Règles de gestion appliquées
- **Sécurité** : Autorisations correctes
- **Performance** : Temps de réponse acceptables

## 📈 Métriques et Rapports

### Couverture de Code
```bash
php run-integration-tests.php --coverage
```
Génère un rapport HTML dans `tests/coverage/`

### Statistiques de Test
Les tests incluent des vérifications de performance :
- Temps de création des données
- Temps de réponse des API
- Nombre d'enregistrements créés

## 🔗 Intégration avec Postman

### Configuration
1. Démarrer le serveur : `php artisan serve`
2. URL de base : `http://localhost:8000/api`
3. Utiliser les tokens générés par les tests

### Endpoints Testés
- `POST /api/auth/login` - Connexion
- `GET /api/sites` - Liste des sites
- `POST /api/pointages/start` - Début pointage
- `POST /api/pointages/{id}/end` - Fin pointage
- `GET /api/employees` - Liste employés (admin)
- Et bien d'autres...

## 🛠️ Dépannage

### Erreurs Communes

**Erreur de connexion MySQL**
```bash
# Vérifier que MySQL est démarré
# Vérifier les credentials dans .env
php run-integration-tests.php --validate
```

**Tables manquantes**
```bash
# Recréer les tables
php run-integration-tests.php --setup
```

**Données corrompues**
```bash
# Nettoyer et recréer
php run-integration-tests.php --cleanup
php run-integration-tests.php --setup
```

### Logs de Debug
Les tests utilisent le logging Laravel. Vérifier :
- `storage/logs/laravel.log`
- Configuration dans `config/logging.php`

## 📝 Bonnes Pratiques

1. **Isolation** : Chaque test est isolé via des transactions
2. **Données propres** : Utiliser les factories pour des données cohérentes
3. **Assertions complètes** : Vérifier la DB ET les réponses HTTP
4. **Performance** : Surveiller les temps d'exécution
5. **Documentation** : Commenter les tests complexes

## 🤝 Contribution

Pour ajouter de nouveaux tests :
1. Créer le fichier de test dans le bon dossier
2. Utiliser les traits `DatabaseTestCase` et `AuthenticationHelpers`
3. Suivre la convention de nommage
4. Ajouter la documentation appropriée
5. Mettre à jour ce README si nécessaire

## 📞 Support

En cas de problème :
1. Vérifier la configuration de la base de données
2. Consulter les logs Laravel
3. Exécuter `php run-integration-tests.php --validate`
4. Vérifier que toutes les dépendances sont installées
