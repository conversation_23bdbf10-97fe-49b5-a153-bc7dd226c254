<?php

namespace Tests\Feature;

use Tests\TestCase;
use Tests\Traits\DatabaseIntegrationTrait;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;

/**
 * Database Integration Test Suite
 * 
 * This class provides utilities to run all database integration tests
 * and verify the connection to the MySQL database.
 */
class DatabaseIntegrationTestSuite extends TestCase
{
    use DatabaseIntegrationTrait;

    /**
     * Test database connection and setup.
     */
    public function test_database_connection_and_setup(): void
    {
        Log::info('Testing database connection and setup');

        // Verify database connection
        $this->assertDatabaseConnection();

        // Verify all required tables exist
        $requiredTables = [
            'users',
            'sites', 
            'pointages',
            'verifications',
            'assignments',
            'logs',
            'personal_access_tokens',
            'password_reset_tokens',
            'sessions'
        ];

        foreach ($requiredTables as $table) {
            $this->assertTrue(
                DB::getSchemaBuilder()->hasTable($table),
                "Table '{$table}' does not exist in the database"
            );
        }

        Log::info('Database connection and setup test completed successfully');
    }

    /**
     * Test database migrations are up to date.
     */
    public function test_database_migrations_are_up_to_date(): void
    {
        Log::info('Testing database migrations are up to date');

        // Run migrations to ensure they're up to date
        Artisan::call('migrate', ['--force' => true]);

        // Verify migration table exists and has records
        $this->assertTrue(DB::getSchemaBuilder()->hasTable('migrations'));
        
        $migrationCount = DB::table('migrations')->count();
        $this->assertGreaterThan(0, $migrationCount, 'No migrations found in database');

        Log::info('Database migrations test completed successfully', [
            'migration_count' => $migrationCount
        ]);
    }

    /**
     * Test database can handle foreign key constraints.
     */
    public function test_database_foreign_key_constraints(): void
    {
        Log::info('Testing database foreign key constraints');

        // Create test data
        $user = $this->createTestUser();
        $site = $this->createTestSite();

        // Test foreign key constraint on assignments table
        $assignment = $this->createTestAssignment($user, $site);
        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $user->id,
            'site_id' => $site->id
        ]);

        // Test foreign key constraint on pointages table
        $pointage = $this->createTestPointage($user, $site);
        $this->assertDatabaseHasRecord('pointages', [
            'user_id' => $user->id,
            'site_id' => $site->id
        ]);

        // Test foreign key constraint on verifications table
        $verification = $this->createTestVerification($user);
        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $user->id
        ]);

        Log::info('Database foreign key constraints test completed successfully');
    }

    /**
     * Test database indexes are working properly.
     */
    public function test_database_indexes_performance(): void
    {
        Log::info('Testing database indexes performance');

        // Create test data for performance testing
        $users = [];
        $sites = [];
        
        for ($i = 1; $i <= 100; $i++) {
            $users[] = $this->createTestUser([
                'name' => "Index Test User {$i}",
                'email' => "indextest{$i}@example.com"
            ]);
            
            $sites[] = $this->createTestSite([
                'name' => "Index Test Site {$i}",
                'latitude' => 33.8869 + ($i * 0.001),
                'longitude' => -9.5375 + ($i * 0.001)
            ]);
        }

        // Test email index on users table
        $startTime = microtime(true);
        $user = DB::table('users')->where('email', '<EMAIL>')->first();
        $emailQueryTime = microtime(true) - $startTime;
        
        $this->assertNotNull($user);
        $this->assertLessThan(0.1, $emailQueryTime, 'Email index query too slow');

        // Test latitude/longitude index on sites table
        $startTime = microtime(true);
        $nearSites = DB::table('sites')
            ->whereBetween('latitude', [33.88, 33.89])
            ->whereBetween('longitude', [-9.54, -9.53])
            ->get();
        $locationQueryTime = microtime(true) - $startTime;
        
        $this->assertGreaterThan(0, $nearSites->count());
        $this->assertLessThan(0.1, $locationQueryTime, 'Location index query too slow');

        Log::info('Database indexes performance test completed successfully', [
            'email_query_time' => $emailQueryTime,
            'location_query_time' => $locationQueryTime,
            'test_users_created' => count($users),
            'test_sites_created' => count($sites)
        ]);
    }

    /**
     * Test database can handle concurrent operations.
     */
    public function test_database_concurrent_operations(): void
    {
        Log::info('Testing database concurrent operations');

        // Create base data
        $user = $this->createTestUser();
        $site = $this->createTestSite();
        $this->createTestAssignment($user, $site);

        // Simulate concurrent pointage operations
        $startTime = microtime(true);
        
        // Start pointage
        $pointage = $this->createTestPointage($user, $site);
        
        // Create verification at the same time
        $verification = $this->createTestVerification($user);
        
        $endTime = microtime(true);
        $operationTime = $endTime - $startTime;

        // Verify both operations completed successfully
        $this->assertDatabaseHasRecord('pointages', ['id' => $pointage->id]);
        $this->assertDatabaseHasRecord('verifications', ['id' => $verification->id]);
        
        // Operations should complete quickly
        $this->assertLessThan(5, $operationTime, 'Concurrent operations too slow');

        Log::info('Database concurrent operations test completed successfully', [
            'operation_time' => $operationTime
        ]);
    }

    /**
     * Test database backup and restore capabilities.
     */
    public function test_database_backup_capabilities(): void
    {
        Log::info('Testing database backup capabilities');

        // Create test data
        $originalUser = $this->createTestUser(['name' => 'Backup Test User']);
        $originalSite = $this->createTestSite(['name' => 'Backup Test Site']);

        // Get initial counts
        $initialUserCount = DB::table('users')->count();
        $initialSiteCount = DB::table('sites')->count();

        // Verify data exists
        $this->assertDatabaseHasRecord('users', ['id' => $originalUser->id]);
        $this->assertDatabaseHasRecord('sites', ['id' => $originalSite->id]);

        // Test data integrity after operations
        $currentUserCount = DB::table('users')->count();
        $currentSiteCount = DB::table('sites')->count();

        $this->assertEquals($initialUserCount, $currentUserCount);
        $this->assertEquals($initialSiteCount, $currentSiteCount);

        Log::info('Database backup capabilities test completed successfully', [
            'user_count' => $currentUserCount,
            'site_count' => $currentSiteCount
        ]);
    }

    /**
     * Test database cleanup and maintenance.
     */
    public function test_database_cleanup_and_maintenance(): void
    {
        Log::info('Testing database cleanup and maintenance');

        // Create test data
        $user = $this->createTestUser();
        $site = $this->createTestSite();
        $assignment = $this->createTestAssignment($user, $site);
        $pointage = $this->createTestPointage($user, $site);
        $verification = $this->createTestVerification($user);

        // Get initial counts
        $initialCounts = [
            'users' => DB::table('users')->count(),
            'sites' => DB::table('sites')->count(),
            'assignments' => DB::table('assignments')->count(),
            'pointages' => DB::table('pointages')->count(),
            'verifications' => DB::table('verifications')->count()
        ];

        // Test cleanup operations (delete test data)
        DB::table('verifications')->where('id', $verification->id)->delete();
        DB::table('pointages')->where('id', $pointage->id)->delete();
        DB::table('assignments')->where('id', $assignment->id)->delete();
        DB::table('sites')->where('id', $site->id)->delete();
        DB::table('users')->where('id', $user->id)->delete();

        // Verify cleanup worked
        $this->assertDatabaseMissingRecord('users', ['id' => $user->id]);
        $this->assertDatabaseMissingRecord('sites', ['id' => $site->id]);
        $this->assertDatabaseMissingRecord('assignments', ['id' => $assignment->id]);
        $this->assertDatabaseMissingRecord('pointages', ['id' => $pointage->id]);
        $this->assertDatabaseMissingRecord('verifications', ['id' => $verification->id]);

        Log::info('Database cleanup and maintenance test completed successfully');
    }

    /**
     * Generate test report for database integration.
     */
    public function test_generate_database_integration_report(): void
    {
        Log::info('Generating database integration test report');

        // Collect database statistics
        $stats = [
            'database_name' => DB::connection()->getDatabaseName(),
            'driver' => DB::connection()->getDriverName(),
            'tables' => [],
            'total_records' => 0
        ];

        $tables = [
            'users', 'sites', 'pointages', 'verifications', 
            'assignments', 'logs', 'personal_access_tokens'
        ];

        foreach ($tables as $table) {
            if (DB::getSchemaBuilder()->hasTable($table)) {
                $count = DB::table($table)->count();
                $stats['tables'][$table] = $count;
                $stats['total_records'] += $count;
            }
        }

        // Log comprehensive report
        Log::info('Database Integration Test Report', $stats);

        // Verify we have a working database with data
        $this->assertGreaterThan(0, $stats['total_records']);
        $this->assertEquals('mysql', $stats['driver']);
        $this->assertEquals('clockin_db', $stats['database_name']);

        // Create a summary for the user
        $summary = [
            'status' => 'SUCCESS',
            'database' => $stats['database_name'],
            'driver' => $stats['driver'],
            'total_tables' => count($stats['tables']),
            'total_records' => $stats['total_records'],
            'test_timestamp' => now()->toISOString()
        ];

        Log::info('Database Integration Test Summary', $summary);

        $this->assertTrue(true, 'Database integration tests completed successfully');
    }
}
