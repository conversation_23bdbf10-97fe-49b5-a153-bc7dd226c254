<?php

namespace Database\Factories;

use App\Models\Assignment;
use App\Models\User;
use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Assignment>
 */
class AssignmentFactory extends Factory
{
    protected $model = Assignment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'site_id' => Site::factory(),
        ];
    }

    /**
     * Assignment pour un utilisateur spécifique
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Assignment pour un site spécifique
     */
    public function forSite(Site $site): static
    {
        return $this->state(fn (array $attributes) => [
            'site_id' => $site->id,
        ]);
    }

    /**
     * Assignment pour un utilisateur et un site spécifiques
     */
    public function forUserAndSite(User $user, Site $site): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'site_id' => $site->id,
        ]);
    }
}
