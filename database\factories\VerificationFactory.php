<?php

namespace Database\Factories;

use App\Models\Verification;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Verification>
 */
class VerificationFactory extends Factory
{
    protected $model = Verification::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'latitude' => $this->faker->latitude(45.0, 49.0),
            'longitude' => $this->faker->longitude(-5.0, 8.0),
            'date_heure' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ];
    }

    /**
     * Vérification pour un utilisateur spécifique
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Vérification récente (dernière heure)
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'date_heure' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }

    /**
     * Vérification d'aujourd'hui
     */
    public function aujourdhui(): static
    {
        return $this->state(fn (array $attributes) => [
            'date_heure' => $this->faker->dateTimeBetween('today', 'now'),
        ]);
    }

    /**
     * Vérification avec coordonnées spécifiques
     */
    public function withCoordinates(float $latitude, float $longitude): static
    {
        return $this->state(fn (array $attributes) => [
            'latitude' => $latitude,
            'longitude' => $longitude,
        ]);
    }

    /**
     * Vérification à une date spécifique
     */
    public function atDate(\DateTime $date): static
    {
        return $this->state(fn (array $attributes) => [
            'date_heure' => $date,
        ]);
    }

    /**
     * Vérification à Paris
     */
    public function paris(): static
    {
        return $this->state(fn (array $attributes) => [
            'latitude' => $this->faker->latitude(48.8, 48.9),
            'longitude' => $this->faker->longitude(2.2, 2.4),
        ]);
    }

    /**
     * Vérification à Lyon
     */
    public function lyon(): static
    {
        return $this->state(fn (array $attributes) => [
            'latitude' => $this->faker->latitude(45.7, 45.8),
            'longitude' => $this->faker->longitude(4.8, 4.9),
        ]);
    }
}
