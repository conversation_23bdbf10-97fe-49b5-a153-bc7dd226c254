<?php

namespace Database\Factories;

use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Site>
 */
class SiteFactory extends Factory
{
    protected $model = Site::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company() . ' - ' . $this->faker->city(),
            'latitude' => $this->faker->latitude(45.0, 49.0), // France approximative
            'longitude' => $this->faker->longitude(-5.0, 8.0), // France approximative
        ];
    }

    /**
     * Site à Paris
     */
    public function paris(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Chantier Paris - ' . $this->faker->streetName(),
            'latitude' => $this->faker->latitude(48.8, 48.9),
            'longitude' => $this->faker->longitude(2.2, 2.4),
        ]);
    }

    /**
     * Site à Lyon
     */
    public function lyon(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Chantier Lyon - ' . $this->faker->streetName(),
            'latitude' => $this->faker->latitude(45.7, 45.8),
            'longitude' => $this->faker->longitude(4.8, 4.9),
        ]);
    }

    /**
     * Site avec nom spécifique
     */
    public function withName(string $name): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $name,
        ]);
    }

    /**
     * Site avec coordonnées spécifiques
     */
    public function withCoordinates(float $latitude, float $longitude): static
    {
        return $this->state(fn (array $attributes) => [
            'latitude' => $latitude,
            'longitude' => $longitude,
        ]);
    }
}
