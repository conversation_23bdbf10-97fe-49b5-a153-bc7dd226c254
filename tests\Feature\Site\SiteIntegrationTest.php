<?php

namespace Tests\Feature\Site;

use Tests\TestCase;
use Tests\Traits\DatabaseTestCase;
use Tests\Traits\AuthenticationHelpers;
use App\Models\User;
use App\Models\Site;
use App\Models\Assignment;
use App\Models\Pointage;

class SiteIntegrationTest extends TestCase
{
    use DatabaseTestCase, AuthenticationHelpers;

    /**
     * Test de récupération de tous les sites
     */
    public function test_can_get_all_sites(): void
    {
        $admin = $this->createTestAdmin();
        
        Site::factory()->count(3)->create();

        $response = $this->authenticatedGet('/api/sites', $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'latitude',
                            'longitude',
                            'created_at',
                            'updated_at'
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('data'));
    }

    /**
     * Test de création d'un site par un admin
     */
    public function test_admin_can_create_site(): void
    {
        $admin = $this->createTestAdmin();

        $siteData = [
            'name' => 'Nouveau Chantier',
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ];

        $response = $this->authenticatedPost('/api/sites', $siteData, $admin);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'site' => [
                            'id',
                            'name',
                            'latitude',
                            'longitude'
                        ]
                    ],
                    'message'
                ])
                ->assertJson([
                    'data' => [
                        'site' => $siteData
                    ]
                ]);

        $this->assertDatabaseHasRecord('sites', $siteData);
    }

    /**
     * Test qu'un employé ne peut pas créer de site
     */
    public function test_employee_cannot_create_site(): void
    {
        $employee = $this->createTestUser();

        $siteData = [
            'name' => 'Nouveau Chantier',
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ];

        $response = $this->authenticatedPost('/api/sites', $siteData, $employee);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Accès non autorisé'
                ]);
    }

    /**
     * Test de récupération d'un site spécifique
     */
    public function test_can_get_specific_site(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create([
            'name' => 'Chantier Test',
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ]);

        $response = $this->authenticatedGet("/api/sites/{$site->id}", $user);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'site' => [
                            'id' => $site->id,
                            'name' => 'Chantier Test',
                            'latitude' => '48.85660000',
                            'longitude' => '2.35220000'
                        ]
                    ]
                ]);
    }

    /**
     * Test de mise à jour d'un site par un admin
     */
    public function test_admin_can_update_site(): void
    {
        $admin = $this->createTestAdmin();
        $site = Site::factory()->create();

        $updateData = [
            'name' => 'Chantier Modifié',
            'latitude' => 45.7640,
            'longitude' => 4.8357
        ];

        $response = $this->authenticatedPut("/api/sites/{$site->id}", $updateData, $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'site' => $updateData
                    ]
                ]);

        $this->assertDatabaseHasRecord('sites', array_merge(['id' => $site->id], $updateData));
    }

    /**
     * Test de suppression d'un site par un admin
     */
    public function test_admin_can_delete_site(): void
    {
        $admin = $this->createTestAdmin();
        $site = Site::factory()->create();

        $response = $this->authenticatedDelete("/api/sites/{$site->id}", $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Site supprimé avec succès'
                ]);

        $this->assertDatabaseMissingRecord('sites', ['id' => $site->id]);
    }

    /**
     * Test qu'un employé ne peut pas supprimer de site
     */
    public function test_employee_cannot_delete_site(): void
    {
        $employee = $this->createTestUser();
        $site = Site::factory()->create();

        $response = $this->authenticatedDelete("/api/sites/{$site->id}", $employee);

        $response->assertStatus(403);
    }

    /**
     * Test de validation des données de site
     */
    public function test_site_validation(): void
    {
        $admin = $this->createTestAdmin();

        $invalidData = [
            'name' => '',
            'latitude' => 'invalid',
            'longitude' => 200 // Longitude invalide
        ];

        $response = $this->authenticatedPost('/api/sites', $invalidData, $admin);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'latitude', 'longitude']);
    }

    /**
     * Test de récupération des sites assignés à un utilisateur
     */
    public function test_can_get_user_assigned_sites(): void
    {
        $user = $this->createTestUser();
        $site1 = Site::factory()->create(['name' => 'Site 1']);
        $site2 = Site::factory()->create(['name' => 'Site 2']);
        $site3 = Site::factory()->create(['name' => 'Site 3']);

        // Assigner seulement site1 et site2 à l'utilisateur
        Assignment::factory()->forUserAndSite($user, $site1)->create();
        Assignment::factory()->forUserAndSite($user, $site2)->create();

        $response = $this->authenticatedGet('/api/sites/assigned', $user);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'latitude',
                            'longitude'
                        ]
                    ]
                ]);

        $sites = $response->json('data');
        $this->assertCount(2, $sites);
        
        $siteNames = collect($sites)->pluck('name')->toArray();
        $this->assertContains('Site 1', $siteNames);
        $this->assertContains('Site 2', $siteNames);
        $this->assertNotContains('Site 3', $siteNames);
    }

    /**
     * Test de calcul de distance depuis un site
     */
    public function test_can_calculate_distance_from_site(): void
    {
        $user = $this->createTestUser();
        $site = Site::factory()->create([
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ]);

        $requestData = [
            'latitude' => 48.8576, // ~100m de différence
            'longitude' => 2.3532
        ];

        $response = $this->authenticatedPost("/api/sites/{$site->id}/distance", $requestData, $user);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'distance_meters',
                        'within_range'
                    ]
                ]);

        $distance = $response->json('data.distance_meters');
        $this->assertIsFloat($distance);
        $this->assertGreaterThan(0, $distance);
        $this->assertLessThan(200, $distance); // Devrait être environ 100m
    }

    /**
     * Test de recherche de sites par nom
     */
    public function test_can_search_sites_by_name(): void
    {
        $user = $this->createTestUser();
        
        Site::factory()->create(['name' => 'Chantier Paris Nord']);
        Site::factory()->create(['name' => 'Chantier Paris Sud']);
        Site::factory()->create(['name' => 'Chantier Lyon']);

        $response = $this->authenticatedGet('/api/sites?search=Paris', $user);

        $response->assertStatus(200);
        
        $sites = $response->json('data');
        $this->assertCount(2, $sites);
        
        foreach ($sites as $site) {
            $this->assertStringContainsString('Paris', $site['name']);
        }
    }

    /**
     * Test de récupération des statistiques d'un site
     */
    public function test_can_get_site_statistics(): void
    {
        $admin = $this->createTestAdmin();
        $site = Site::factory()->create();
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Créer des assignments et pointages
        Assignment::factory()->forUserAndSite($user1, $site)->create();
        Assignment::factory()->forUserAndSite($user2, $site)->create();
        
        Pointage::factory()->forUser($user1)->forSite($site)->termine()->create();
        Pointage::factory()->forUser($user2)->forSite($site)->enCours()->create();

        $response = $this->authenticatedGet("/api/sites/{$site->id}/statistics", $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_employees',
                        'active_pointages',
                        'total_pointages',
                        'average_duration'
                    ]
                ]);

        $stats = $response->json('data');
        $this->assertEquals(2, $stats['total_employees']);
        $this->assertEquals(1, $stats['active_pointages']);
        $this->assertEquals(2, $stats['total_pointages']);
    }
}
