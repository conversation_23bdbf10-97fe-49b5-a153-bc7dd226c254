<?php

/**
 * Script de test simple pour vérifier les APIs ClockIn
 */

$baseUrl = 'http://localhost:8000/api';

function makeRequest($method, $url, $data = null, $token = null) {
    $ch = curl_init();
    
    $headers = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    if ($token) {
        $headers[] = 'Authorization: Bearer ' . $token;
    }
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true),
        'raw' => $response
    ];
}

echo "=== Test des APIs ClockIn ===\n\n";

// 1. Test de santé
echo "1. Test de santé de l'API...\n";
$response = makeRequest('GET', $baseUrl . '/health');
echo "Status: " . $response['status'] . "\n";
echo "Response: " . $response['raw'] . "\n\n";

// 2. Inscription d'un admin
echo "2. Inscription d'un administrateur...\n";
$adminData = [
    'name' => 'Admin Test',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'password_confirmation' => 'password123',
    'role' => 'admin'
];

$response = makeRequest('POST', $baseUrl . '/auth/register', $adminData);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] === 201) {
    $adminToken = $response['body']['data']['token'];
    $adminId = $response['body']['data']['user']['id'];
    echo "✅ Admin créé avec succès. Token: " . substr($adminToken, 0, 20) . "...\n";
} else {
    echo "❌ Erreur lors de la création de l'admin\n";
    echo "Response: " . $response['raw'] . "\n";
}
echo "\n";

// 3. Inscription d'un employé
echo "3. Inscription d'un employé...\n";
$employeeData = [
    'name' => 'Employee Test',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'password_confirmation' => 'password123',
    'role' => 'employee'
];

$response = makeRequest('POST', $baseUrl . '/auth/register', $employeeData);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] === 201) {
    $employeeToken = $response['body']['data']['token'];
    $employeeId = $response['body']['data']['user']['id'];
    echo "✅ Employé créé avec succès. Token: " . substr($employeeToken, 0, 20) . "...\n";
} else {
    echo "❌ Erreur lors de la création de l'employé\n";
    echo "Response: " . $response['raw'] . "\n";
}
echo "\n";

if (!isset($adminToken) || !isset($employeeToken)) {
    echo "❌ Impossible de continuer sans les tokens d'authentification\n";
    exit(1);
}

// 4. Création d'un site par l'admin
echo "4. Création d'un site par l'admin...\n";
$siteData = [
    'name' => 'Chantier Test',
    'latitude' => 48.8566,
    'longitude' => 2.3522
];

$response = makeRequest('POST', $baseUrl . '/sites', $siteData, $adminToken);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] === 201) {
    $siteId = $response['body']['data']['site']['id'];
    echo "✅ Site créé avec succès. ID: " . $siteId . "\n";
} else {
    echo "❌ Erreur lors de la création du site\n";
    echo "Response: " . $response['raw'] . "\n";
}
echo "\n";

if (!isset($siteId)) {
    echo "❌ Impossible de continuer sans site\n";
    exit(1);
}

// 5. Assignment de l'employé au site
echo "5. Assignment de l'employé au site...\n";
$assignmentData = ['site_id' => $siteId];

$response = makeRequest('POST', $baseUrl . '/employees/' . $employeeId . '/assign', $assignmentData, $adminToken);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] === 201) {
    echo "✅ Employé assigné au site avec succès\n";
} else {
    echo "❌ Erreur lors de l'assignment\n";
    echo "Response: " . $response['raw'] . "\n";
}
echo "\n";

// 6. Début de pointage par l'employé
echo "6. Début de pointage par l'employé...\n";
$pointageData = [
    'site_id' => $siteId,
    'latitude' => 48.8566,
    'longitude' => 2.3522
];

$response = makeRequest('POST', $baseUrl . '/pointages/start', $pointageData, $employeeToken);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] === 201) {
    $pointageId = $response['body']['data']['pointage']['id'];
    echo "✅ Pointage commencé avec succès. ID: " . $pointageId . "\n";
} else {
    echo "❌ Erreur lors du début de pointage\n";
    echo "Response: " . $response['raw'] . "\n";
}
echo "\n";

// 7. Vérification du pointage en cours
echo "7. Vérification du pointage en cours...\n";
$response = makeRequest('GET', $baseUrl . '/pointages/current', null, $employeeToken);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] === 200 && $response['body']['data']['pointage']) {
    echo "✅ Pointage en cours trouvé\n";
} else {
    echo "❌ Aucun pointage en cours trouvé\n";
    echo "Response: " . $response['raw'] . "\n";
}
echo "\n";

// 8. Création d'une vérification
echo "8. Création d'une vérification...\n";
$verificationData = [
    'latitude' => 48.8576,
    'longitude' => 2.3532
];

$response = makeRequest('POST', $baseUrl . '/verifications', $verificationData, $employeeToken);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] === 201) {
    echo "✅ Vérification créée avec succès\n";
} else {
    echo "❌ Erreur lors de la création de la vérification\n";
    echo "Response: " . $response['raw'] . "\n";
}
echo "\n";

// 9. Tableau de bord admin
echo "9. Consultation du tableau de bord admin...\n";
$response = makeRequest('GET', $baseUrl . '/admin/dashboard', null, $adminToken);
echo "Status: " . $response['status'] . "\n";
if ($response['status'] === 200) {
    echo "✅ Tableau de bord accessible\n";
    $dashboard = $response['body']['data'];
    echo "   - Employés: " . $dashboard['total_employees'] . "\n";
    echo "   - Sites: " . $dashboard['total_sites'] . "\n";
    echo "   - Pointages actifs: " . $dashboard['active_pointages'] . "\n";
} else {
    echo "❌ Erreur lors de l'accès au tableau de bord\n";
    echo "Response: " . $response['raw'] . "\n";
}
echo "\n";

// 10. Fin de pointage
if (isset($pointageId)) {
    echo "10. Fin de pointage...\n";
    $endData = [
        'latitude' => 48.8576,
        'longitude' => 2.3532
    ];

    $response = makeRequest('POST', $baseUrl . '/pointages/' . $pointageId . '/end', $endData, $employeeToken);
    echo "Status: " . $response['status'] . "\n";
    if ($response['status'] === 200) {
        echo "✅ Pointage terminé avec succès\n";
    } else {
        echo "❌ Erreur lors de la fin de pointage\n";
        echo "Response: " . $response['raw'] . "\n";
    }
    echo "\n";
}

echo "=== Tests terminés ===\n";
echo "\n📋 Instructions pour Postman :\n";
echo "1. Importez la collection : postman/ClockIn_API_Collection.json\n";
echo "2. Importez l'environnement : postman/ClockIn_Environment.json\n";
echo "3. Configurez l'URL de base : http://localhost:8000\n";
echo "4. Exécutez les requêtes dans l'ordre de la collection\n";
echo "5. Les tokens seront automatiquement sauvegardés dans l'environnement\n";
echo "\n🔗 URL de base pour Postman : http://localhost:8000/api\n";
echo "🗄️ Base de données accessible via : http://localhost:8080/phpmyadmin\n";
