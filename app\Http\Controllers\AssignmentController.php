<?php

namespace App\Http\Controllers;

use App\Models\Assignment;
use App\Models\User;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class AssignmentController extends Controller
{
    /**
     * Liste de toutes les assignments (admin uniquement)
     */
    public function index(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $assignments = Assignment::with('user', 'site')->get();

        return response()->json([
            'success' => true,
            'data' => $assignments
        ]);
    }

    /**
     * Créer une nouvelle assignment (admin uniquement)
     */
    public function store(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'site_id' => 'required|exists:sites,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        // Vérifier que l'utilisateur est un employé
        $user = User::find($request->user_id);
        if ($user->role !== 'employee') {
            return response()->json([
                'success' => false,
                'message' => 'Seuls les employés peuvent être assignés à des sites'
            ], 422);
        }

        // Vérifier si l'assignment existe déjà
        $existingAssignment = Assignment::where('user_id', $request->user_id)
                                       ->where('site_id', $request->site_id)
                                       ->first();

        if ($existingAssignment) {
            return response()->json([
                'success' => false,
                'message' => 'Cet employé est déjà assigné à ce site'
            ], 422);
        }

        $assignment = Assignment::create($request->only(['user_id', 'site_id']));

        return response()->json([
            'success' => true,
            'data' => [
                'assignment' => $assignment->load('user', 'site')
            ],
            'message' => 'Assignment créée avec succès'
        ], 201);
    }

    /**
     * Afficher une assignment spécifique (admin uniquement)
     */
    public function show(Request $request, Assignment $assignment): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'assignment' => $assignment->load('user', 'site')
            ]
        ]);
    }

    /**
     * Supprimer une assignment (admin uniquement)
     */
    public function destroy(Request $request, Assignment $assignment): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $assignment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Assignment supprimée avec succès'
        ]);
    }

    /**
     * Assignments d'un utilisateur spécifique (admin uniquement)
     */
    public function byUser(Request $request, User $user): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $assignments = $user->assignments()->with('site')->get();

        return response()->json([
            'success' => true,
            'data' => $assignments
        ]);
    }

    /**
     * Assignments d'un site spécifique (admin uniquement)
     */
    public function bySite(Request $request, Site $site): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $assignments = $site->assignments()->with('user')->get();

        return response()->json([
            'success' => true,
            'data' => $assignments
        ]);
    }

    /**
     * Mes assignments (employé)
     */
    public function myAssignments(Request $request): JsonResponse
    {
        $user = $request->user();
        $assignments = $user->assignments()->with('site')->get();

        return response()->json([
            'success' => true,
            'data' => $assignments
        ]);
    }

    /**
     * Vérifier si un utilisateur est assigné à un site (admin uniquement)
     */
    public function checkAssignment(Request $request, User $user, Site $site): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $assignment = Assignment::where('user_id', $user->id)
                                ->where('site_id', $site->id)
                                ->first();

        return response()->json([
            'success' => true,
            'data' => [
                'is_assigned' => !is_null($assignment)
            ]
        ]);
    }

    /**
     * Statistiques des assignments (admin uniquement)
     */
    public function statistics(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $totalAssignments = Assignment::count();
        $employeesWithAssignments = Assignment::distinct('user_id')->count();
        $sitesWithAssignments = Assignment::distinct('site_id')->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_assignments' => $totalAssignments,
                'employees_with_assignments' => $employeesWithAssignments,
                'sites_with_assignments' => $sitesWithAssignments,
                'average_assignments_per_employee' => $employeesWithAssignments > 0 ? round($totalAssignments / $employeesWithAssignments, 2) : 0,
                'average_employees_per_site' => $sitesWithAssignments > 0 ? round($totalAssignments / $sitesWithAssignments, 2) : 0
            ]
        ]);
    }

    /**
     * Créer des assignments en masse (admin uniquement)
     */
    public function bulkCreate(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'site_id' => 'required|exists:sites,id',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $created = 0;
        foreach ($request->user_ids as $userId) {
            $existing = Assignment::where('user_id', $userId)
                                 ->where('site_id', $request->site_id)
                                 ->first();
            
            if (!$existing) {
                Assignment::create([
                    'user_id' => $userId,
                    'site_id' => $request->site_id
                ]);
                $created++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "{$created} assignments créées avec succès"
        ], 201);
    }

    /**
     * Supprimer des assignments en masse (admin uniquement)
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'assignment_ids' => 'required|array',
            'assignment_ids.*' => 'exists:assignments,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $deleted = Assignment::whereIn('id', $request->assignment_ids)->delete();

        return response()->json([
            'success' => true,
            'message' => "{$deleted} assignments supprimées avec succès"
        ]);
    }
}
