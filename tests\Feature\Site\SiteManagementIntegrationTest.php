<?php

namespace Tests\Feature\Site;

use Tests\TestCase;
use Tests\Traits\DatabaseIntegrationTrait;
use App\Models\Site;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\Sanctum;

class SiteManagementIntegrationTest extends TestCase
{
    use DatabaseIntegrationTrait;

    /**
     * Test admin can list all sites.
     */
    public function test_admin_can_list_all_sites(): void
    {
        Log::info('Testing admin can list all sites');

        // Create admin user
        $admin = $this->createTestAdmin();
        Sanctum::actingAs($admin);

        // Create multiple sites
        $site1 = $this->createTestSite(['name' => 'Site 1', 'latitude' => 33.8869, 'longitude' => -9.5375]);
        $site2 = $this->createTestSite(['name' => 'Site 2', 'latitude' => 34.0209, 'longitude' => -6.8416]);
        $site3 = $this->createTestSite(['name' => 'Site 3', 'latitude' => 31.6295, 'longitude' => -7.9811]);

        // Request sites list
        $response = $this->getJson('/api/sites');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => ['id', 'name', 'latitude', 'longitude', 'created_at', 'updated_at']
                     ]
                 ]);

        // Verify all sites are in response
        $responseData = $response->json();
        $siteIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($site1->id, $siteIds);
        $this->assertContains($site2->id, $siteIds);
        $this->assertContains($site3->id, $siteIds);

        // Verify data is from database
        $this->assertDatabaseHasRecord('sites', ['id' => $site1->id, 'name' => 'Site 1']);
        $this->assertDatabaseHasRecord('sites', ['id' => $site2->id, 'name' => 'Site 2']);
        $this->assertDatabaseHasRecord('sites', ['id' => $site3->id, 'name' => 'Site 3']);

        Log::info('Admin list sites test completed successfully');
    }

    /**
     * Test admin can view specific site details.
     */
    public function test_admin_can_view_site_details(): void
    {
        Log::info('Testing admin can view site details');

        // Create admin and site
        $admin = $this->createTestAdmin();
        $site = $this->createTestSite([
            'name' => 'Detailed Site',
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ]);
        Sanctum::actingAs($admin);

        // Request site details
        $response = $this->getJson("/api/sites/{$site->id}");

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJson([
                     'id' => $site->id,
                     'name' => 'Detailed Site',
                     'latitude' => '33.88690000',
                     'longitude' => '-9.53750000'
                 ]);

        // Verify site exists in database
        $this->assertDatabaseHasRecord('sites', [
            'id' => $site->id,
            'name' => 'Detailed Site',
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ]);

        Log::info('Admin view site details test completed successfully', ['site_id' => $site->id]);
    }

    /**
     * Test admin can create new site.
     */
    public function test_admin_can_create_new_site(): void
    {
        Log::info('Testing admin can create new site');

        // Create admin user
        $admin = $this->createTestAdmin();
        Sanctum::actingAs($admin);

        $siteData = [
            'name' => 'New Site',
            'latitude' => 35.7796,
            'longitude' => -5.8122
        ];

        // Create site
        $response = $this->postJson('/api/sites', $siteData);

        // Assert successful creation
        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'id', 'name', 'latitude', 'longitude', 'created_at', 'updated_at'
                 ])
                 ->assertJson([
                     'name' => 'New Site',
                     'latitude' => '35.77960000',
                     'longitude' => '-5.81220000'
                 ]);

        // Verify site is stored in database
        $this->assertDatabaseHasRecord('sites', [
            'name' => 'New Site',
            'latitude' => 35.7796,
            'longitude' => -5.8122
        ]);

        // Get created site from database
        $createdSite = Site::where('name', 'New Site')->first();
        $this->assertNotNull($createdSite);

        Log::info('Admin create site test completed successfully', ['site_id' => $createdSite->id]);
    }

    /**
     * Test admin can update site information.
     */
    public function test_admin_can_update_site_information(): void
    {
        Log::info('Testing admin can update site information');

        // Create admin and site
        $admin = $this->createTestAdmin();
        $site = $this->createTestSite([
            'name' => 'Original Site',
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ]);
        Sanctum::actingAs($admin);

        $updateData = [
            'name' => 'Updated Site',
            'latitude' => 34.0209,
            'longitude' => -6.8416
        ];

        // Update site
        $response = $this->putJson("/api/sites/{$site->id}", $updateData);

        // Assert successful update
        $response->assertStatus(200)
                 ->assertJson([
                     'id' => $site->id,
                     'name' => 'Updated Site',
                     'latitude' => '34.02090000',
                     'longitude' => '-6.84160000'
                 ]);

        // Verify changes in database
        $this->assertDatabaseHasRecord('sites', [
            'id' => $site->id,
            'name' => 'Updated Site',
            'latitude' => 34.0209,
            'longitude' => -6.8416
        ]);

        // Verify old data is no longer in database
        $this->assertDatabaseMissingRecord('sites', [
            'id' => $site->id,
            'name' => 'Original Site',
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ]);

        Log::info('Admin update site test completed successfully', ['site_id' => $site->id]);
    }

    /**
     * Test admin can delete site.
     */
    public function test_admin_can_delete_site(): void
    {
        Log::info('Testing admin can delete site');

        // Create admin and site
        $admin = $this->createTestAdmin();
        $site = $this->createTestSite([
            'name' => 'To Be Deleted',
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ]);
        Sanctum::actingAs($admin);

        // Verify site exists before deletion
        $this->assertDatabaseHasRecord('sites', [
            'id' => $site->id,
            'name' => 'To Be Deleted'
        ]);

        // Delete site
        $response = $this->deleteJson("/api/sites/{$site->id}");

        // Assert successful deletion
        $response->assertStatus(200)
                 ->assertJson([
                     'message' => 'Site deleted successfully'
                 ]);

        // Verify site is removed from database
        $this->assertDatabaseMissingRecord('sites', [
            'id' => $site->id,
            'name' => 'To Be Deleted'
        ]);

        Log::info('Admin delete site test completed successfully', ['site_id' => $site->id]);
    }

    /**
     * Test employee can view assigned sites.
     */
    public function test_employee_can_view_assigned_sites(): void
    {
        Log::info('Testing employee can view assigned sites');

        // Create employee and sites
        $employee = $this->createTestUser();
        $assignedSite = $this->createTestSite(['name' => 'Assigned Site']);
        $unassignedSite = $this->createTestSite(['name' => 'Unassigned Site']);
        
        // Create assignment
        $this->createTestAssignment($employee, $assignedSite);
        
        Sanctum::actingAs($employee);

        // Request assigned sites
        $response = $this->getJson('/api/my-sites');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => ['id', 'name', 'latitude', 'longitude']
                     ]
                 ]);

        // Verify only assigned site is in response
        $responseData = $response->json();
        $siteIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($assignedSite->id, $siteIds);
        $this->assertNotContains($unassignedSite->id, $siteIds);

        // Verify assignment exists in database
        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $employee->id,
            'site_id' => $assignedSite->id
        ]);

        Log::info('Employee view assigned sites test completed successfully', ['employee_id' => $employee->id]);
    }

    /**
     * Test employee cannot access admin site endpoints.
     */
    public function test_employee_cannot_access_admin_site_endpoints(): void
    {
        Log::info('Testing employee cannot access admin site endpoints');

        // Create regular employee
        $employee = $this->createTestUser();
        Sanctum::actingAs($employee);

        // Try to access admin endpoints
        $endpoints = [
            ['method' => 'get', 'url' => '/api/sites'],
            ['method' => 'post', 'url' => '/api/sites', 'data' => ['name' => 'Test Site', 'latitude' => 33.8869, 'longitude' => -9.5375]],
            ['method' => 'put', 'url' => '/api/sites/1', 'data' => ['name' => 'Updated Site']],
            ['method' => 'delete', 'url' => '/api/sites/1']
        ];

        foreach ($endpoints as $endpoint) {
            $response = match($endpoint['method']) {
                'get' => $this->getJson($endpoint['url']),
                'post' => $this->postJson($endpoint['url'], $endpoint['data'] ?? []),
                'put' => $this->putJson($endpoint['url'], $endpoint['data'] ?? []),
                'delete' => $this->deleteJson($endpoint['url'])
            };

            // Assert forbidden access
            $response->assertStatus(403);
        }

        Log::info('Employee site access restriction test completed successfully');
    }

    /**
     * Test site creation with invalid coordinates.
     */
    public function test_site_creation_with_invalid_coordinates(): void
    {
        Log::info('Testing site creation with invalid coordinates');

        // Create admin user
        $admin = $this->createTestAdmin();
        Sanctum::actingAs($admin);

        $invalidSiteData = [
            'name' => 'Invalid Site',
            'latitude' => 91.0000, // Invalid latitude (> 90)
            'longitude' => 181.0000 // Invalid longitude (> 180)
        ];

        // Try to create site with invalid coordinates
        $response = $this->postJson('/api/sites', $invalidSiteData);

        // Assert validation error
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['latitude', 'longitude']);

        // Verify site is not created in database
        $this->assertDatabaseMissingRecord('sites', [
            'name' => 'Invalid Site'
        ]);

        Log::info('Invalid coordinates site creation test completed successfully');
    }
}
