<?php

namespace Tests;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;

class TestHelper
{
    /**
     * Préparer la base de données pour les tests
     */
    public static function setupDatabase(): void
    {
        // Créer la base de données de test si elle n'existe pas
        self::createTestDatabase();
        
        // Exécuter les migrations
        Artisan::call('migrate:fresh', ['--env' => 'testing']);
        
        // Vérifier que toutes les tables sont créées
        self::verifyTables();
    }

    /**
     * Créer la base de données de test
     */
    private static function createTestDatabase(): void
    {
        $database = env('DB_DATABASE', 'clockin_db_test');
        
        try {
            // Se connecter sans spécifier de base de données
            $pdo = new \PDO(
                'mysql:host=' . env('DB_HOST', '127.0.0.1') . ';port=' . env('DB_PORT', '3306'),
                env('DB_USERNAME', 'root'),
                env('DB_PASSWORD', ''),
                [\PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION]
            );
            
            // Créer la base de données si elle n'existe pas
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            echo "Base de données de test '{$database}' créée ou vérifiée.\n";
            
        } catch (\PDOException $e) {
            throw new \Exception("Impossible de créer la base de données de test: " . $e->getMessage());
        }
    }

    /**
     * Vérifier que toutes les tables nécessaires existent
     */
    private static function verifyTables(): void
    {
        $requiredTables = [
            'users',
            'sites',
            'pointages',
            'assignments',
            'verifications',
            'logs',
            'personal_access_tokens',
            'migrations'
        ];

        foreach ($requiredTables as $table) {
            if (!Schema::hasTable($table)) {
                throw new \Exception("La table '{$table}' n'existe pas dans la base de données de test");
            }
        }

        echo "Toutes les tables requises sont présentes.\n";
    }

    /**
     * Nettoyer la base de données après les tests
     */
    public static function cleanupDatabase(): void
    {
        $tables = [
            'logs',
            'verifications', 
            'pointages',
            'assignments',
            'personal_access_tokens',
            'sites',
            'users'
        ];

        DB::statement('SET FOREIGN_KEY_CHECKS=0');
        
        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                DB::table($table)->truncate();
            }
        }
        
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
        
        echo "Base de données nettoyée.\n";
    }

    /**
     * Vérifier la connexion à la base de données
     */
    public static function checkDatabaseConnection(): bool
    {
        try {
            DB::connection()->getPdo();
            echo "Connexion à la base de données réussie.\n";
            return true;
        } catch (\Exception $e) {
            echo "Erreur de connexion à la base de données: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * Afficher les informations de configuration
     */
    public static function displayConfiguration(): void
    {
        echo "\n=== Configuration des Tests ===\n";
        echo "Environnement: " . env('APP_ENV') . "\n";
        echo "Base de données: " . env('DB_CONNECTION') . "\n";
        echo "Host: " . env('DB_HOST') . "\n";
        echo "Port: " . env('DB_PORT') . "\n";
        echo "Database: " . env('DB_DATABASE') . "\n";
        echo "Username: " . env('DB_USERNAME') . "\n";
        echo "================================\n\n";
    }

    /**
     * Exécuter tous les tests d'intégration
     */
    public static function runIntegrationTests(): void
    {
        echo "Démarrage des tests d'intégration...\n\n";
        
        // Vérifier la configuration
        self::displayConfiguration();
        
        // Vérifier la connexion
        if (!self::checkDatabaseConnection()) {
            throw new \Exception("Impossible de se connecter à la base de données");
        }
        
        // Préparer la base de données
        self::setupDatabase();
        
        // Exécuter les tests
        $testSuites = [
            'tests/Feature/Integration/DatabaseIntegrationTest.php',
            'tests/Feature/Auth/AuthenticationIntegrationTest.php',
            'tests/Feature/Site/SiteIntegrationTest.php',
            'tests/Feature/Employee/EmployeeIntegrationTest.php',
            'tests/Feature/Pointage/PointageIntegrationTest.php',
            'tests/Feature/Assignment/AssignmentIntegrationTest.php',
            'tests/Feature/Verification/VerificationIntegrationTest.php',
            'tests/Feature/Integration/CompleteWorkflowIntegrationTest.php'
        ];
        
        foreach ($testSuites as $testSuite) {
            echo "Exécution de: {$testSuite}\n";
            $output = shell_exec("php artisan test {$testSuite} --env=testing");
            echo $output . "\n";
        }
        
        echo "Tests d'intégration terminés.\n";
    }

    /**
     * Générer un rapport de couverture des tests
     */
    public static function generateCoverageReport(): void
    {
        echo "Génération du rapport de couverture...\n";
        
        $command = "php artisan test --coverage-html=tests/coverage --env=testing";
        $output = shell_exec($command);
        
        echo $output . "\n";
        echo "Rapport de couverture généré dans tests/coverage/\n";
    }

    /**
     * Valider la structure des données de test
     */
    public static function validateTestData(): void
    {
        echo "Validation de la structure des données de test...\n";
        
        // Vérifier les contraintes de clés étrangères
        $constraints = DB::select("
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE REFERENCED_TABLE_SCHEMA = ? 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ", [env('DB_DATABASE')]);
        
        echo "Contraintes de clés étrangères trouvées: " . count($constraints) . "\n";
        
        foreach ($constraints as $constraint) {
            echo "- {$constraint->TABLE_NAME}.{$constraint->COLUMN_NAME} -> {$constraint->REFERENCED_TABLE_NAME}.{$constraint->REFERENCED_COLUMN_NAME}\n";
        }
        
        // Vérifier les index
        $indexes = DB::select("
            SELECT 
                TABLE_NAME,
                INDEX_NAME,
                COLUMN_NAME
            FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = ?
            AND INDEX_NAME != 'PRIMARY'
            ORDER BY TABLE_NAME, INDEX_NAME
        ", [env('DB_DATABASE')]);
        
        echo "\nIndex trouvés: " . count($indexes) . "\n";
        
        echo "Validation terminée.\n";
    }

    /**
     * Créer des données de test de base
     */
    public static function seedTestData(): void
    {
        echo "Création des données de test de base...\n";
        
        Artisan::call('db:seed', ['--env' => 'testing']);
        
        echo "Données de test créées.\n";
    }
}
