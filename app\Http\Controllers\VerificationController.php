<?php

namespace App\Http\Controllers;

use App\Models\Verification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class VerificationController extends Controller
{
    /**
     * Liste des vérifications de l'utilisateur connecté
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = $user->verifications();

        // Filtrage par date
        if ($request->has('date')) {
            $query->whereDate('date_heure', $request->date);
        }

        // Filtrage par période
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->whereBetween('date_heure', [$request->start_date, $request->end_date]);
        }

        $verifications = $query->orderBy('date_heure', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $verifications
        ]);
    }

    /**
     * Créer une nouvelle vérification
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        // Vérifier la limite quotidienne (50 vérifications par jour)
        $todayCount = $user->verifications()
                          ->whereDate('date_heure', Carbon::today())
                          ->count();

        if ($todayCount >= 50) {
            return response()->json([
                'success' => false,
                'message' => 'Limite quotidienne de vérifications atteinte'
            ], 429);
        }

        $verification = Verification::create([
            'user_id' => $user->id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'date_heure' => now()
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'verification' => $verification
            ],
            'message' => 'Vérification créée avec succès'
        ], 201);
    }

    /**
     * Afficher une vérification spécifique
     */
    public function show(Request $request, Verification $verification): JsonResponse
    {
        $user = $request->user();

        // Vérifier les autorisations
        if ($verification->user_id !== $user->id && !$user->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'verification' => $verification
            ]
        ]);
    }

    /**
     * Supprimer une vérification
     */
    public function destroy(Request $request, Verification $verification): JsonResponse
    {
        $user = $request->user();

        // Vérifier les autorisations
        if ($verification->user_id !== $user->id && !$user->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $verification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Vérification supprimée avec succès'
        ]);
    }

    /**
     * Vérifications récentes (dernière heure)
     */
    public function recent(Request $request): JsonResponse
    {
        $user = $request->user();
        $verifications = $user->verifications()
                             ->where('date_heure', '>=', Carbon::now()->subHour())
                             ->orderBy('date_heure', 'desc')
                             ->get();

        return response()->json([
            'success' => true,
            'data' => $verifications
        ]);
    }

    /**
     * Statistiques des vérifications de l'utilisateur
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = $request->user();

        $totalVerifications = $user->verifications()->count();
        $verificationsToday = $user->verifications()
                                  ->whereDate('date_heure', Carbon::today())
                                  ->count();
        $verificationsThisWeek = $user->verifications()
                                     ->whereBetween('date_heure', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
                                     ->count();
        $verificationsThisMonth = $user->verifications()
                                      ->whereBetween('date_heure', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()])
                                      ->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_verifications' => $totalVerifications,
                'verifications_today' => $verificationsToday,
                'verifications_this_week' => $verificationsThisWeek,
                'verifications_this_month' => $verificationsThisMonth,
                'average_per_day' => $totalVerifications > 0 ? round($totalVerifications / max(1, Carbon::now()->diffInDays($user->created_at)), 2) : 0
            ]
        ]);
    }

    /**
     * Créer une vérification automatique (lors du pointage)
     */
    public function createAutomatic(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'action' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        $verification = Verification::create([
            'user_id' => $user->id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'date_heure' => now()
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'verification' => $verification
            ],
            'message' => 'Vérification automatique créée'
        ], 201);
    }

    /**
     * Calculer la distance entre deux vérifications
     */
    public function calculateDistance(Request $request, Verification $verification1, Verification $verification2): JsonResponse
    {
        $user = $request->user();

        // Vérifier les autorisations
        if (($verification1->user_id !== $user->id || $verification2->user_id !== $user->id) && !$user->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        // Calculer la distance (formule de Haversine)
        $earthRadius = 6371000; // Rayon de la Terre en mètres

        $lat1 = deg2rad($verification1->latitude);
        $lon1 = deg2rad($verification1->longitude);
        $lat2 = deg2rad($verification2->latitude);
        $lon2 = deg2rad($verification2->longitude);

        $latDelta = $lat2 - $lat1;
        $lonDelta = $lon2 - $lon1;

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos($lat1) * cos($lat2) *
             sin($lonDelta / 2) * sin($lonDelta / 2);
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        $distance = $earthRadius * $c;

        return response()->json([
            'success' => true,
            'data' => [
                'distance_meters' => $distance,
                'verification1' => $verification1,
                'verification2' => $verification2
            ]
        ]);
    }

    /**
     * Toutes les vérifications (admin uniquement)
     */
    public function adminIndex(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $verifications = Verification::with('user')
                                   ->orderBy('date_heure', 'desc')
                                   ->get();

        return response()->json([
            'success' => true,
            'data' => $verifications
        ]);
    }
}
