<?php

namespace Tests\Traits;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait DatabaseIntegrationTrait
{
    use RefreshDatabase;

    /**
     * Setup the test environment for database integration.
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Ensure we're using MySQL for integration tests
        $this->assertDatabaseConnection();
        
        // Log test start
        Log::info('Starting database integration test: ' . $this->getName());
    }

    /**
     * Clean up after test.
     */
    protected function tearDown(): void
    {
        // Log test completion
        Log::info('Completed database integration test: ' . $this->getName());
        
        parent::tearDown();
    }

    /**
     * Assert that we're connected to the correct database.
     */
    protected function assertDatabaseConnection(): void
    {
        $connection = DB::connection();
        $this->assertEquals('mysql', $connection->getDriverName());
        $this->assertEquals('clockin_db', $connection->getDatabaseName());
    }

    /**
     * Create test data and return the created records.
     */
    protected function createTestUser(array $attributes = []): \App\Models\User
    {
        $userData = array_merge([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'employee'
        ], $attributes);

        $user = \App\Models\User::create($userData);
        
        Log::info('Created test user', ['user_id' => $user->id, 'email' => $user->email]);
        
        return $user;
    }

    /**
     * Create test admin user.
     */
    protected function createTestAdmin(array $attributes = []): \App\Models\User
    {
        return $this->createTestUser(array_merge(['role' => 'admin'], $attributes));
    }

    /**
     * Create test site.
     */
    protected function createTestSite(array $attributes = []): \App\Models\Site
    {
        $siteData = array_merge([
            'name' => 'Test Site',
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ], $attributes);

        $site = \App\Models\Site::create($siteData);
        
        Log::info('Created test site', ['site_id' => $site->id, 'name' => $site->name]);
        
        return $site;
    }

    /**
     * Create test pointage.
     */
    protected function createTestPointage(\App\Models\User $user, \App\Models\Site $site, array $attributes = []): \App\Models\Pointage
    {
        $pointageData = array_merge([
            'user_id' => $user->id,
            'site_id' => $site->id,
            'debut_pointage' => now(),
            'debut_latitude' => 33.8869,
            'debut_longitude' => -9.5375
        ], $attributes);

        $pointage = \App\Models\Pointage::create($pointageData);
        
        Log::info('Created test pointage', [
            'pointage_id' => $pointage->id,
            'user_id' => $user->id,
            'site_id' => $site->id
        ]);
        
        return $pointage;
    }

    /**
     * Create test assignment.
     */
    protected function createTestAssignment(\App\Models\User $user, \App\Models\Site $site): \App\Models\Assignment
    {
        $assignment = \App\Models\Assignment::create([
            'user_id' => $user->id,
            'site_id' => $site->id
        ]);
        
        Log::info('Created test assignment', [
            'assignment_id' => $assignment->id,
            'user_id' => $user->id,
            'site_id' => $site->id
        ]);
        
        return $assignment;
    }

    /**
     * Create test verification.
     */
    protected function createTestVerification(\App\Models\User $user, array $attributes = []): \App\Models\Verification
    {
        $verificationData = array_merge([
            'user_id' => $user->id,
            'latitude' => 33.8869,
            'longitude' => -9.5375,
            'date_heure' => now()
        ], $attributes);

        $verification = \App\Models\Verification::create($verificationData);
        
        Log::info('Created test verification', [
            'verification_id' => $verification->id,
            'user_id' => $user->id
        ]);
        
        return $verification;
    }

    /**
     * Assert database has record with specific attributes.
     */
    protected function assertDatabaseHasRecord(string $table, array $attributes): void
    {
        $this->assertDatabaseHas($table, $attributes);
        Log::info("Verified database has record in {$table}", $attributes);
    }

    /**
     * Assert database missing record with specific attributes.
     */
    protected function assertDatabaseMissingRecord(string $table, array $attributes): void
    {
        $this->assertDatabaseMissing($table, $attributes);
        Log::info("Verified database missing record in {$table}", $attributes);
    }

    /**
     * Get database record count for a table.
     */
    protected function getDatabaseRecordCount(string $table): int
    {
        $count = DB::table($table)->count();
        Log::info("Database record count for {$table}: {$count}");
        return $count;
    }

    /**
     * Truncate all test tables.
     */
    protected function truncateTestTables(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        $tables = ['logs', 'assignments', 'verifications', 'pointages', 'sites', 'users'];
        
        foreach ($tables as $table) {
            DB::table($table)->truncate();
            Log::info("Truncated table: {$table}");
        }
        
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
}
