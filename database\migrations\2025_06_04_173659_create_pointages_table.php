<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pointages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('site_id')->constrained()->onDelete('cascade');
            $table->dateTime('debut_pointage');
            $table->dateTime('fin_pointage')->nullable();
            $table->time('duree')->nullable();
            $table->decimal('debut_latitude', 10, 8)->nullable();
            $table->decimal('debut_longitude', 11, 8)->nullable();
            $table->decimal('fin_latitude', 10, 8)->nullable();
            $table->decimal('fin_longitude', 11, 8)->nullable();
            $table->timestamps();

            $table->index('user_id');
            $table->index('site_id');
            $table->index('debut_pointage');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pointages');
    }
};
