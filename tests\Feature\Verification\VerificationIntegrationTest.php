<?php

namespace Tests\Feature\Verification;

use Tests\TestCase;
use Tests\Traits\DatabaseIntegrationTrait;
use App\Models\Verification;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\Sanctum;
use Carbon\Carbon;

class VerificationIntegrationTest extends TestCase
{
    use DatabaseIntegrationTrait;

    /**
     * Test employee can create location verification.
     */
    public function test_employee_can_create_location_verification(): void
    {
        Log::info('Testing employee can create location verification');

        // Create employee
        $employee = $this->createTestUser();
        Sanctum::actingAs($employee);

        $verificationData = [
            'latitude' => 33.8869,
            'longitude' => -9.5375,
            'date_heure' => Carbon::now()->toISOString()
        ];

        // Create verification
        $response = $this->postJson('/api/verifications', $verificationData);

        // Assert successful creation
        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'id', 'user_id', 'latitude', 'longitude', 'date_heure', 'created_at', 'updated_at'
                 ])
                 ->assertJson([
                     'user_id' => $employee->id,
                     'latitude' => '33.88690000',
                     'longitude' => '-9.53750000'
                 ]);

        // Verify verification is stored in database
        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $employee->id,
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ]);

        // Get created verification from database
        $verification = Verification::where('user_id', $employee->id)->first();
        $this->assertNotNull($verification);

        Log::info('Employee create verification test completed successfully', [
            'employee_id' => $employee->id,
            'verification_id' => $verification->id
        ]);
    }

    /**
     * Test employee can view their verification history.
     */
    public function test_employee_can_view_verification_history(): void
    {
        Log::info('Testing employee can view verification history');

        // Create employee
        $employee = $this->createTestUser();

        // Create multiple verifications
        $verification1 = $this->createTestVerification($employee, [
            'latitude' => 33.8869,
            'longitude' => -9.5375,
            'date_heure' => Carbon::now()->subDays(2)
        ]);
        
        $verification2 = $this->createTestVerification($employee, [
            'latitude' => 34.0209,
            'longitude' => -6.8416,
            'date_heure' => Carbon::now()->subDay()
        ]);

        Sanctum::actingAs($employee);

        // Request verification history
        $response = $this->getJson('/api/verifications/history');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => ['id', 'latitude', 'longitude', 'date_heure', 'created_at']
                     ]
                 ]);

        // Verify verifications are in response
        $responseData = $response->json();
        $verificationIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($verification1->id, $verificationIds);
        $this->assertContains($verification2->id, $verificationIds);

        // Verify data comes from database
        $this->assertDatabaseHasRecord('verifications', ['id' => $verification1->id]);
        $this->assertDatabaseHasRecord('verifications', ['id' => $verification2->id]);

        Log::info('Employee view verification history test completed successfully', [
            'employee_id' => $employee->id
        ]);
    }

    /**
     * Test admin can view all verifications.
     */
    public function test_admin_can_view_all_verifications(): void
    {
        Log::info('Testing admin can view all verifications');

        // Create admin and employees
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);

        // Create verifications for different employees
        $verification1 = $this->createTestVerification($employee1);
        $verification2 = $this->createTestVerification($employee2);

        Sanctum::actingAs($admin);

        // Request all verifications
        $response = $this->getJson('/api/admin/verifications');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id', 'user_id', 'latitude', 'longitude', 'date_heure',
                             'user' => ['id', 'name', 'email']
                         ]
                     ]
                 ]);

        // Verify all verifications are in response
        $responseData = $response->json();
        $verificationIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($verification1->id, $verificationIds);
        $this->assertContains($verification2->id, $verificationIds);

        // Verify data comes from database
        $this->assertDatabaseHasRecord('verifications', ['id' => $verification1->id, 'user_id' => $employee1->id]);
        $this->assertDatabaseHasRecord('verifications', ['id' => $verification2->id, 'user_id' => $employee2->id]);

        Log::info('Admin view all verifications test completed successfully');
    }

    /**
     * Test verification with invalid coordinates.
     */
    public function test_verification_with_invalid_coordinates(): void
    {
        Log::info('Testing verification with invalid coordinates');

        // Create employee
        $employee = $this->createTestUser();
        Sanctum::actingAs($employee);

        $invalidVerificationData = [
            'latitude' => 91.0000, // Invalid latitude (> 90)
            'longitude' => 181.0000, // Invalid longitude (> 180)
            'date_heure' => Carbon::now()->toISOString()
        ];

        // Try to create verification with invalid coordinates
        $response = $this->postJson('/api/verifications', $invalidVerificationData);

        // Assert validation error
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['latitude', 'longitude']);

        // Verify verification is not created in database
        $this->assertDatabaseMissingRecord('verifications', [
            'user_id' => $employee->id,
            'latitude' => 91.0000,
            'longitude' => 181.0000
        ]);

        Log::info('Invalid coordinates verification test completed successfully');
    }

    /**
     * Test verification filtering by date range.
     */
    public function test_verification_filtering_by_date_range(): void
    {
        Log::info('Testing verification filtering by date range');

        // Create employee
        $employee = $this->createTestUser();

        // Create verifications with different dates
        $oldVerification = $this->createTestVerification($employee, [
            'date_heure' => Carbon::now()->subWeek()
        ]);
        
        $recentVerification = $this->createTestVerification($employee, [
            'date_heure' => Carbon::now()->subDay()
        ]);

        Sanctum::actingAs($employee);

        // Request verifications for last 3 days
        $fromDate = Carbon::now()->subDays(3)->toDateString();
        $toDate = Carbon::now()->toDateString();
        
        $response = $this->getJson("/api/verifications/history?from={$fromDate}&to={$toDate}");

        // Assert successful response
        $response->assertStatus(200);

        // Verify only recent verification is in response
        $responseData = $response->json();
        $verificationIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($recentVerification->id, $verificationIds);
        $this->assertNotContains($oldVerification->id, $verificationIds);

        // Verify both verifications exist in database
        $this->assertDatabaseHasRecord('verifications', ['id' => $oldVerification->id]);
        $this->assertDatabaseHasRecord('verifications', ['id' => $recentVerification->id]);

        Log::info('Verification date filtering test completed successfully', [
            'employee_id' => $employee->id
        ]);
    }

    /**
     * Test employee cannot view other employees' verifications.
     */
    public function test_employee_cannot_view_other_employees_verifications(): void
    {
        Log::info('Testing employee cannot view other employees verifications');

        // Create two employees
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);

        // Create verifications for both employees
        $verification1 = $this->createTestVerification($employee1);
        $verification2 = $this->createTestVerification($employee2);

        // Authenticate as employee1
        Sanctum::actingAs($employee1);

        // Request verification history
        $response = $this->getJson('/api/verifications/history');

        // Assert successful response
        $response->assertStatus(200);

        // Verify only employee1's verification is in response
        $responseData = $response->json();
        $verificationIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($verification1->id, $verificationIds);
        $this->assertNotContains($verification2->id, $verificationIds);

        // Verify both verifications exist in database
        $this->assertDatabaseHasRecord('verifications', ['id' => $verification1->id, 'user_id' => $employee1->id]);
        $this->assertDatabaseHasRecord('verifications', ['id' => $verification2->id, 'user_id' => $employee2->id]);

        Log::info('Employee verification isolation test completed successfully');
    }

    /**
     * Test verification with future date is rejected.
     */
    public function test_verification_with_future_date_is_rejected(): void
    {
        Log::info('Testing verification with future date is rejected');

        // Create employee
        $employee = $this->createTestUser();
        Sanctum::actingAs($employee);

        $futureVerificationData = [
            'latitude' => 33.8869,
            'longitude' => -9.5375,
            'date_heure' => Carbon::now()->addDay()->toISOString() // Future date
        ];

        // Try to create verification with future date
        $response = $this->postJson('/api/verifications', $futureVerificationData);

        // Assert validation error
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['date_heure']);

        // Verify verification is not created in database
        $this->assertDatabaseMissingRecord('verifications', [
            'user_id' => $employee->id
        ]);

        Log::info('Future date verification test completed successfully');
    }

    /**
     * Test verification statistics for admin.
     */
    public function test_verification_statistics_for_admin(): void
    {
        Log::info('Testing verification statistics for admin');

        // Create admin and employees
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);

        // Create verifications
        $this->createTestVerification($employee1, ['date_heure' => Carbon::now()->subHours(2)]);
        $this->createTestVerification($employee1, ['date_heure' => Carbon::now()->subHour()]);
        $this->createTestVerification($employee2, ['date_heure' => Carbon::now()->subMinutes(30)]);

        Sanctum::actingAs($admin);

        // Request verification statistics
        $response = $this->getJson('/api/admin/verifications/stats');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'total_verifications',
                     'today_verifications',
                     'verifications_by_user' => [
                         '*' => ['user_id', 'user_name', 'count']
                     ]
                 ]);

        // Verify statistics
        $responseData = $response->json();
        $this->assertEquals(3, $responseData['total_verifications']);
        $this->assertGreaterThanOrEqual(1, $responseData['today_verifications']);

        // Verify data comes from database
        $totalInDb = Verification::count();
        $this->assertEquals($totalInDb, $responseData['total_verifications']);

        Log::info('Verification statistics test completed successfully');
    }

    /**
     * Test bulk verification creation.
     */
    public function test_bulk_verification_creation(): void
    {
        Log::info('Testing bulk verification creation');

        // Create employee
        $employee = $this->createTestUser();
        Sanctum::actingAs($employee);

        $bulkVerificationData = [
            'verifications' => [
                [
                    'latitude' => 33.8869,
                    'longitude' => -9.5375,
                    'date_heure' => Carbon::now()->subHours(2)->toISOString()
                ],
                [
                    'latitude' => 33.8870,
                    'longitude' => -9.5376,
                    'date_heure' => Carbon::now()->subHour()->toISOString()
                ]
            ]
        ];

        // Create bulk verifications
        $response = $this->postJson('/api/verifications/bulk', $bulkVerificationData);

        // Assert successful creation
        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'message',
                     'created_count',
                     'verifications' => [
                         '*' => ['id', 'user_id', 'latitude', 'longitude', 'date_heure']
                     ]
                 ]);

        // Verify verifications are stored in database
        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $employee->id,
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ]);

        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $employee->id,
            'latitude' => 33.8870,
            'longitude' => -9.5376
        ]);

        // Verify count
        $verificationCount = Verification::where('user_id', $employee->id)->count();
        $this->assertEquals(2, $verificationCount);

        Log::info('Bulk verification creation test completed successfully', [
            'employee_id' => $employee->id,
            'created_count' => 2
        ]);
    }
}
