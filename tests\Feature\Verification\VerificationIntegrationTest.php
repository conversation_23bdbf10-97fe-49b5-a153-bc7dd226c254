<?php

namespace Tests\Feature\Verification;

use Tests\TestCase;
use Tests\Traits\DatabaseTestCase;
use Tests\Traits\AuthenticationHelpers;
use App\Models\User;
use App\Models\Verification;
use Carbon\Carbon;

class VerificationIntegrationTest extends TestCase
{
    use DatabaseTestCase, AuthenticationHelpers;

    /**
     * Test de création d'une vérification de localisation
     */
    public function test_user_can_create_verification(): void
    {
        $user = $this->createTestUser();

        $verificationData = [
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ];

        $response = $this->authenticatedPost('/api/verifications', $verificationData, $user);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'verification' => [
                            'id',
                            'user_id',
                            'latitude',
                            'longitude',
                            'date_heure'
                        ]
                    ],
                    'message'
                ]);

        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $user->id,
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ]);
    }

    /**
     * Test de récupération des vérifications de l'utilisateur
     */
    public function test_user_can_get_own_verifications(): void
    {
        $user = $this->createTestUser();
        
        Verification::factory()->forUser($user)->count(3)->create();

        $response = $this->authenticatedGet('/api/verifications', $user);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'latitude',
                            'longitude',
                            'date_heure'
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('data'));
    }

    /**
     * Test qu'un admin peut voir toutes les vérifications
     */
    public function test_admin_can_get_all_verifications(): void
    {
        $admin = $this->createTestAdmin();
        $user1 = User::factory()->create(['role' => 'employee']);
        $user2 = User::factory()->create(['role' => 'employee']);

        Verification::factory()->forUser($user1)->count(2)->create();
        Verification::factory()->forUser($user2)->count(3)->create();

        $response = $this->authenticatedGet('/api/admin/verifications', $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'user' => [
                                'id',
                                'name',
                                'email'
                            ],
                            'latitude',
                            'longitude',
                            'date_heure'
                        ]
                    ]
                ]);

        $this->assertCount(5, $response->json('data'));
    }

    /**
     * Test qu'un employé ne peut pas voir les vérifications des autres
     */
    public function test_employee_cannot_see_other_verifications(): void
    {
        $employee = $this->createTestUser();

        $response = $this->authenticatedGet('/api/admin/verifications', $employee);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Accès non autorisé'
                ]);
    }

    /**
     * Test de récupération d'une vérification spécifique
     */
    public function test_user_can_get_specific_verification(): void
    {
        $user = $this->createTestUser();
        $verification = Verification::factory()->forUser($user)->create([
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ]);

        $response = $this->authenticatedGet("/api/verifications/{$verification->id}", $user);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'verification' => [
                            'id' => $verification->id,
                            'latitude' => '48.85660000',
                            'longitude' => '2.35220000'
                        ]
                    ]
                ]);
    }

    /**
     * Test qu'un utilisateur ne peut pas voir les vérifications d'un autre
     */
    public function test_user_cannot_see_other_user_verification(): void
    {
        $user1 = $this->createTestUser();
        $user2 = User::factory()->create(['role' => 'employee']);
        
        $verification = Verification::factory()->forUser($user2)->create();

        $response = $this->authenticatedGet("/api/verifications/{$verification->id}", $user1);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Accès non autorisé'
                ]);
    }

    /**
     * Test de suppression d'une vérification
     */
    public function test_user_can_delete_own_verification(): void
    {
        $user = $this->createTestUser();
        $verification = Verification::factory()->forUser($user)->create();

        $response = $this->authenticatedDelete("/api/verifications/{$verification->id}", $user);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Vérification supprimée avec succès'
                ]);

        $this->assertDatabaseMissingRecord('verifications', ['id' => $verification->id]);
    }

    /**
     * Test de validation des coordonnées
     */
    public function test_verification_coordinates_validation(): void
    {
        $user = $this->createTestUser();

        $invalidData = [
            'latitude' => 200, // Latitude invalide
            'longitude' => 'invalid'
        ];

        $response = $this->authenticatedPost('/api/verifications', $invalidData, $user);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['latitude', 'longitude']);
    }

    /**
     * Test de filtrage des vérifications par date
     */
    public function test_can_filter_verifications_by_date(): void
    {
        $user = $this->createTestUser();

        // Créer des vérifications à différentes dates
        Verification::factory()->forUser($user)->create([
            'date_heure' => Carbon::today()
        ]);
        Verification::factory()->forUser($user)->create([
            'date_heure' => Carbon::yesterday()
        ]);
        Verification::factory()->forUser($user)->create([
            'date_heure' => Carbon::today()->subDays(2)
        ]);

        $today = Carbon::today()->format('Y-m-d');
        $response = $this->authenticatedGet("/api/verifications?date={$today}", $user);

        $response->assertStatus(200);
        $verifications = $response->json('data');
        $this->assertCount(1, $verifications);
    }

    /**
     * Test de filtrage des vérifications par période
     */
    public function test_can_filter_verifications_by_date_range(): void
    {
        $user = $this->createTestUser();

        $startDate = Carbon::today()->subDays(7);
        $endDate = Carbon::today();

        // Créer des vérifications dans et hors de la période
        Verification::factory()->forUser($user)->create([
            'date_heure' => $startDate->copy()->addDays(2)
        ]);
        Verification::factory()->forUser($user)->create([
            'date_heure' => $startDate->copy()->addDays(4)
        ]);
        Verification::factory()->forUser($user)->create([
            'date_heure' => $startDate->copy()->subDays(2) // Hors période
        ]);

        $response = $this->authenticatedGet(
            "/api/verifications?start_date={$startDate->format('Y-m-d')}&end_date={$endDate->format('Y-m-d')}", 
            $user
        );

        $response->assertStatus(200);
        $verifications = $response->json('data');
        $this->assertCount(2, $verifications);
    }

    /**
     * Test de récupération des vérifications récentes
     */
    public function test_can_get_recent_verifications(): void
    {
        $user = $this->createTestUser();

        // Créer des vérifications récentes et anciennes
        Verification::factory()->forUser($user)->recent()->count(3)->create();
        Verification::factory()->forUser($user)->create([
            'date_heure' => Carbon::now()->subDays(2)
        ]);

        $response = $this->authenticatedGet('/api/verifications/recent', $user);

        $response->assertStatus(200);
        $verifications = $response->json('data');
        $this->assertCount(3, $verifications);
    }

    /**
     * Test de calcul de distance entre vérifications
     */
    public function test_can_calculate_distance_between_verifications(): void
    {
        $user = $this->createTestUser();
        
        $verification1 = Verification::factory()->forUser($user)->create([
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ]);
        
        $verification2 = Verification::factory()->forUser($user)->create([
            'latitude' => 48.8576, // ~100m de différence
            'longitude' => 2.3532
        ]);

        $response = $this->authenticatedGet(
            "/api/verifications/{$verification1->id}/distance/{$verification2->id}", 
            $user
        );

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'distance_meters',
                        'verification1',
                        'verification2'
                    ]
                ]);

        $distance = $response->json('data.distance_meters');
        $this->assertIsFloat($distance);
        $this->assertGreaterThan(0, $distance);
        $this->assertLessThan(200, $distance);
    }

    /**
     * Test de récupération des statistiques de vérifications
     */
    public function test_user_can_get_verification_statistics(): void
    {
        $user = $this->createTestUser();

        // Créer des vérifications sur différentes périodes
        Verification::factory()->forUser($user)->aujourdhui()->count(2)->create();
        Verification::factory()->forUser($user)->create([
            'date_heure' => Carbon::yesterday()
        ]);
        Verification::factory()->forUser($user)->create([
            'date_heure' => Carbon::today()->subWeek()
        ]);

        $response = $this->authenticatedGet('/api/verifications/statistics', $user);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_verifications',
                        'verifications_today',
                        'verifications_this_week',
                        'verifications_this_month',
                        'average_per_day'
                    ]
                ]);

        $stats = $response->json('data');
        $this->assertEquals(4, $stats['total_verifications']);
        $this->assertEquals(2, $stats['verifications_today']);
    }

    /**
     * Test de création automatique de vérification lors du pointage
     */
    public function test_verification_is_created_automatically_on_pointage(): void
    {
        $user = $this->createTestUser();

        $verificationData = [
            'latitude' => 48.8566,
            'longitude' => 2.3522,
            'action' => 'pointage_start'
        ];

        $response = $this->authenticatedPost('/api/verifications/auto', $verificationData, $user);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Vérification automatique créée'
                ]);

        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $user->id,
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ]);
    }

    /**
     * Test de limitation du nombre de vérifications par jour
     */
    public function test_verification_daily_limit(): void
    {
        $user = $this->createTestUser();

        // Créer le maximum de vérifications autorisées par jour (ex: 50)
        Verification::factory()->forUser($user)->aujourdhui()->count(50)->create();

        $verificationData = [
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ];

        $response = $this->authenticatedPost('/api/verifications', $verificationData, $user);

        $response->assertStatus(429)
                ->assertJson([
                    'success' => false,
                    'message' => 'Limite quotidienne de vérifications atteinte'
                ]);
    }
}
