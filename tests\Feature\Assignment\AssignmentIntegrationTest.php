<?php

namespace Tests\Feature\Assignment;

use Tests\TestCase;
use Tests\Traits\DatabaseTestCase;
use Tests\Traits\AuthenticationHelpers;
use App\Models\User;
use App\Models\Site;
use App\Models\Assignment;

class AssignmentIntegrationTest extends TestCase
{
    use DatabaseTestCase, AuthenticationHelpers;

    /**
     * Test qu'un admin peut récupérer toutes les assignments
     */
    public function test_admin_can_get_all_assignments(): void
    {
        $admin = $this->createTestAdmin();
        $user = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();
        
        Assignment::factory()->forUserAndSite($user, $site)->count(3)->create();

        $response = $this->authenticatedGet('/api/assignments', $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'user' => [
                                'id',
                                'name',
                                'email'
                            ],
                            'site' => [
                                'id',
                                'name',
                                'latitude',
                                'longitude'
                            ],
                            'created_at',
                            'updated_at'
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('data'));
    }

    /**
     * Test qu'un employé ne peut pas voir toutes les assignments
     */
    public function test_employee_cannot_get_all_assignments(): void
    {
        $employee = $this->createTestUser();

        $response = $this->authenticatedGet('/api/assignments', $employee);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Accès non autorisé'
                ]);
    }

    /**
     * Test de création d'une assignment
     */
    public function test_admin_can_create_assignment(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();

        $assignmentData = [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ];

        $response = $this->authenticatedPost('/api/assignments', $assignmentData, $admin);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'assignment' => [
                            'id',
                            'user_id',
                            'site_id'
                        ]
                    ],
                    'message'
                ]);

        $this->assertDatabaseHasRecord('assignments', $assignmentData);
    }

    /**
     * Test qu'on ne peut pas créer une assignment dupliquée
     */
    public function test_cannot_create_duplicate_assignment(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();

        // Créer la première assignment
        Assignment::factory()->forUserAndSite($employee, $site)->create();

        $assignmentData = [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ];

        $response = $this->authenticatedPost('/api/assignments', $assignmentData, $admin);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Cet employé est déjà assigné à ce site'
                ]);
    }

    /**
     * Test de récupération d'une assignment spécifique
     */
    public function test_admin_can_get_specific_assignment(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();
        $assignment = Assignment::factory()->forUserAndSite($employee, $site)->create();

        $response = $this->authenticatedGet("/api/assignments/{$assignment->id}", $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'assignment' => [
                            'id' => $assignment->id,
                            'user_id' => $employee->id,
                            'site_id' => $site->id
                        ]
                    ]
                ]);
    }

    /**
     * Test de suppression d'une assignment
     */
    public function test_admin_can_delete_assignment(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();
        $assignment = Assignment::factory()->forUserAndSite($employee, $site)->create();

        $response = $this->authenticatedDelete("/api/assignments/{$assignment->id}", $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Assignment supprimée avec succès'
                ]);

        $this->assertDatabaseMissingRecord('assignments', ['id' => $assignment->id]);
    }

    /**
     * Test de récupération des assignments par utilisateur
     */
    public function test_can_get_assignments_by_user(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site1 = Site::factory()->create();
        $site2 = Site::factory()->create();

        Assignment::factory()->forUserAndSite($employee, $site1)->create();
        Assignment::factory()->forUserAndSite($employee, $site2)->create();

        $response = $this->authenticatedGet("/api/assignments/user/{$employee->id}", $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'site' => [
                                'id',
                                'name'
                            ]
                        ]
                    ]
                ]);

        $this->assertCount(2, $response->json('data'));
    }

    /**
     * Test de récupération des assignments par site
     */
    public function test_can_get_assignments_by_site(): void
    {
        $admin = $this->createTestAdmin();
        $employee1 = User::factory()->create(['role' => 'employee']);
        $employee2 = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();

        Assignment::factory()->forUserAndSite($employee1, $site)->create();
        Assignment::factory()->forUserAndSite($employee2, $site)->create();

        $response = $this->authenticatedGet("/api/assignments/site/{$site->id}", $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'user' => [
                                'id',
                                'name',
                                'email'
                            ]
                        ]
                    ]
                ]);

        $this->assertCount(2, $response->json('data'));
    }

    /**
     * Test qu'un employé peut voir ses propres assignments
     */
    public function test_employee_can_see_own_assignments(): void
    {
        $employee = $this->createTestUser();
        $site1 = Site::factory()->create();
        $site2 = Site::factory()->create();

        Assignment::factory()->forUserAndSite($employee, $site1)->create();
        Assignment::factory()->forUserAndSite($employee, $site2)->create();

        $response = $this->authenticatedGet('/api/assignments/my', $employee);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'site' => [
                                'id',
                                'name',
                                'latitude',
                                'longitude'
                            ]
                        ]
                    ]
                ]);

        $this->assertCount(2, $response->json('data'));
    }

    /**
     * Test de validation des données d'assignment
     */
    public function test_assignment_validation(): void
    {
        $admin = $this->createTestAdmin();

        $invalidData = [
            'user_id' => 999, // Utilisateur inexistant
            'site_id' => 999  // Site inexistant
        ];

        $response = $this->authenticatedPost('/api/assignments', $invalidData, $admin);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['user_id', 'site_id']);
    }

    /**
     * Test qu'on ne peut assigner qu'un employé (pas un admin)
     */
    public function test_cannot_assign_admin_to_site(): void
    {
        $admin = $this->createTestAdmin();
        $anotherAdmin = User::factory()->create(['role' => 'admin']);
        $site = Site::factory()->create();

        $assignmentData = [
            'user_id' => $anotherAdmin->id,
            'site_id' => $site->id
        ];

        $response = $this->authenticatedPost('/api/assignments', $assignmentData, $admin);

        $response->assertStatus(422)
                ->assertJson([
                    'success' => false,
                    'message' => 'Seuls les employés peuvent être assignés à des sites'
                ]);
    }

    /**
     * Test de création d'assignments en masse
     */
    public function test_admin_can_create_bulk_assignments(): void
    {
        $admin = $this->createTestAdmin();
        $employee1 = User::factory()->create(['role' => 'employee']);
        $employee2 = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();

        $bulkData = [
            'site_id' => $site->id,
            'user_ids' => [$employee1->id, $employee2->id]
        ];

        $response = $this->authenticatedPost('/api/assignments/bulk', $bulkData, $admin);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => '2 assignments créées avec succès'
                ]);

        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $employee1->id,
            'site_id' => $site->id
        ]);

        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $employee2->id,
            'site_id' => $site->id
        ]);
    }

    /**
     * Test de suppression d'assignments en masse
     */
    public function test_admin_can_delete_bulk_assignments(): void
    {
        $admin = $this->createTestAdmin();
        $employee1 = User::factory()->create(['role' => 'employee']);
        $employee2 = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();

        $assignment1 = Assignment::factory()->forUserAndSite($employee1, $site)->create();
        $assignment2 = Assignment::factory()->forUserAndSite($employee2, $site)->create();

        $bulkData = [
            'assignment_ids' => [$assignment1->id, $assignment2->id]
        ];

        $response = $this->authenticatedDelete('/api/assignments/bulk', $bulkData, $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => '2 assignments supprimées avec succès'
                ]);

        $this->assertDatabaseMissingRecord('assignments', ['id' => $assignment1->id]);
        $this->assertDatabaseMissingRecord('assignments', ['id' => $assignment2->id]);
    }

    /**
     * Test de vérification si un utilisateur est assigné à un site
     */
    public function test_can_check_if_user_is_assigned_to_site(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();

        Assignment::factory()->forUserAndSite($employee, $site)->create();

        $response = $this->authenticatedGet("/api/assignments/check/{$employee->id}/{$site->id}", $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'is_assigned' => true
                    ]
                ]);
    }

    /**
     * Test de récupération des statistiques d'assignments
     */
    public function test_admin_can_get_assignment_statistics(): void
    {
        $admin = $this->createTestAdmin();
        $employees = User::factory()->count(5)->create(['role' => 'employee']);
        $sites = Site::factory()->count(3)->create();

        // Créer diverses assignments
        foreach ($employees as $employee) {
            foreach ($sites->take(2) as $site) {
                Assignment::factory()->forUserAndSite($employee, $site)->create();
            }
        }

        $response = $this->authenticatedGet('/api/assignments/statistics', $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_assignments',
                        'employees_with_assignments',
                        'sites_with_assignments',
                        'average_assignments_per_employee',
                        'average_employees_per_site'
                    ]
                ]);

        $stats = $response->json('data');
        $this->assertEquals(10, $stats['total_assignments']); // 5 employés × 2 sites
        $this->assertEquals(5, $stats['employees_with_assignments']);
        $this->assertEquals(2, $stats['sites_with_assignments']);
    }
}
