<?php

namespace Tests\Feature\Assignment;

use Tests\TestCase;
use Tests\Traits\DatabaseIntegrationTrait;
use App\Models\Assignment;
use App\Models\User;
use App\Models\Site;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\Sanctum;

class AssignmentIntegrationTest extends TestCase
{
    use DatabaseIntegrationTrait;

    /**
     * Test admin can create employee assignment to site.
     */
    public function test_admin_can_create_employee_assignment(): void
    {
        Log::info('Testing admin can create employee assignment');

        // Create admin, employee and site
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser(['name' => 'Assigned Employee']);
        $site = $this->createTestSite(['name' => 'Assignment Site']);
        
        Sanctum::actingAs($admin);

        $assignmentData = [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ];

        // Create assignment
        $response = $this->postJson('/api/assignments', $assignmentData);

        // Assert successful creation
        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'id', 'user_id', 'site_id', 'created_at', 'updated_at',
                     'user' => ['id', 'name', 'email'],
                     'site' => ['id', 'name', 'latitude', 'longitude']
                 ])
                 ->assertJson([
                     'user_id' => $employee->id,
                     'site_id' => $site->id
                 ]);

        // Verify assignment is stored in database
        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        // Get created assignment from database
        $assignment = Assignment::where('user_id', $employee->id)
                               ->where('site_id', $site->id)
                               ->first();
        $this->assertNotNull($assignment);

        Log::info('Admin create assignment test completed successfully', [
            'admin_id' => $admin->id,
            'employee_id' => $employee->id,
            'site_id' => $site->id,
            'assignment_id' => $assignment->id
        ]);
    }

    /**
     * Test admin can view all assignments.
     */
    public function test_admin_can_view_all_assignments(): void
    {
        Log::info('Testing admin can view all assignments');

        // Create admin, employees and sites
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);
        $site1 = $this->createTestSite(['name' => 'Site 1']);
        $site2 = $this->createTestSite(['name' => 'Site 2']);

        // Create multiple assignments
        $assignment1 = $this->createTestAssignment($employee1, $site1);
        $assignment2 = $this->createTestAssignment($employee2, $site2);
        $assignment3 = $this->createTestAssignment($employee1, $site2);

        Sanctum::actingAs($admin);

        // Request all assignments
        $response = $this->getJson('/api/assignments');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id', 'user_id', 'site_id', 'created_at', 'updated_at',
                             'user' => ['id', 'name', 'email'],
                             'site' => ['id', 'name', 'latitude', 'longitude']
                         ]
                     ]
                 ]);

        // Verify all assignments are in response
        $responseData = $response->json();
        $assignmentIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($assignment1->id, $assignmentIds);
        $this->assertContains($assignment2->id, $assignmentIds);
        $this->assertContains($assignment3->id, $assignmentIds);

        // Verify data comes from database
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment1->id]);
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment2->id]);
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment3->id]);

        Log::info('Admin view all assignments test completed successfully');
    }

    /**
     * Test admin can view assignments for specific employee.
     */
    public function test_admin_can_view_assignments_for_specific_employee(): void
    {
        Log::info('Testing admin can view assignments for specific employee');

        // Create admin, employees and sites
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Target Employee']);
        $employee2 = $this->createTestUser(['name' => 'Other Employee']);
        $site1 = $this->createTestSite(['name' => 'Site 1']);
        $site2 = $this->createTestSite(['name' => 'Site 2']);

        // Create assignments
        $assignment1 = $this->createTestAssignment($employee1, $site1);
        $assignment2 = $this->createTestAssignment($employee1, $site2);
        $assignment3 = $this->createTestAssignment($employee2, $site1); // Different employee

        Sanctum::actingAs($admin);

        // Request assignments for specific employee
        $response = $this->getJson("/api/employees/{$employee1->id}/assignments");

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id', 'user_id', 'site_id',
                             'site' => ['id', 'name', 'latitude', 'longitude']
                         ]
                     ]
                 ]);

        // Verify only target employee's assignments are in response
        $responseData = $response->json();
        $assignmentIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($assignment1->id, $assignmentIds);
        $this->assertContains($assignment2->id, $assignmentIds);
        $this->assertNotContains($assignment3->id, $assignmentIds);

        // Verify all assignments exist in database
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment1->id, 'user_id' => $employee1->id]);
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment2->id, 'user_id' => $employee1->id]);
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment3->id, 'user_id' => $employee2->id]);

        Log::info('Admin view employee assignments test completed successfully', [
            'employee_id' => $employee1->id
        ]);
    }

    /**
     * Test admin can view assignments for specific site.
     */
    public function test_admin_can_view_assignments_for_specific_site(): void
    {
        Log::info('Testing admin can view assignments for specific site');

        // Create admin, employees and sites
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);
        $site1 = $this->createTestSite(['name' => 'Target Site']);
        $site2 = $this->createTestSite(['name' => 'Other Site']);

        // Create assignments
        $assignment1 = $this->createTestAssignment($employee1, $site1);
        $assignment2 = $this->createTestAssignment($employee2, $site1);
        $assignment3 = $this->createTestAssignment($employee1, $site2); // Different site

        Sanctum::actingAs($admin);

        // Request assignments for specific site
        $response = $this->getJson("/api/sites/{$site1->id}/assignments");

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id', 'user_id', 'site_id',
                             'user' => ['id', 'name', 'email']
                         ]
                     ]
                 ]);

        // Verify only target site's assignments are in response
        $responseData = $response->json();
        $assignmentIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($assignment1->id, $assignmentIds);
        $this->assertContains($assignment2->id, $assignmentIds);
        $this->assertNotContains($assignment3->id, $assignmentIds);

        // Verify all assignments exist in database
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment1->id, 'site_id' => $site1->id]);
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment2->id, 'site_id' => $site1->id]);
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment3->id, 'site_id' => $site2->id]);

        Log::info('Admin view site assignments test completed successfully', [
            'site_id' => $site1->id
        ]);
    }

    /**
     * Test admin can delete assignment.
     */
    public function test_admin_can_delete_assignment(): void
    {
        Log::info('Testing admin can delete assignment');

        // Create admin, employee, site and assignment
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        $assignment = $this->createTestAssignment($employee, $site);

        Sanctum::actingAs($admin);

        // Verify assignment exists before deletion
        $this->assertDatabaseHasRecord('assignments', [
            'id' => $assignment->id,
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        // Delete assignment
        $response = $this->deleteJson("/api/assignments/{$assignment->id}");

        // Assert successful deletion
        $response->assertStatus(200)
                 ->assertJson([
                     'message' => 'Assignment deleted successfully'
                 ]);

        // Verify assignment is removed from database
        $this->assertDatabaseMissingRecord('assignments', [
            'id' => $assignment->id,
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        Log::info('Admin delete assignment test completed successfully', [
            'assignment_id' => $assignment->id
        ]);
    }

    /**
     * Test cannot create duplicate assignment.
     */
    public function test_cannot_create_duplicate_assignment(): void
    {
        Log::info('Testing cannot create duplicate assignment');

        // Create admin, employee and site
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        
        // Create initial assignment
        $existingAssignment = $this->createTestAssignment($employee, $site);
        
        Sanctum::actingAs($admin);

        $duplicateAssignmentData = [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ];

        // Try to create duplicate assignment
        $response = $this->postJson('/api/assignments', $duplicateAssignmentData);

        // Assert validation error
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['user_id']);

        // Verify only one assignment exists
        $assignmentCount = Assignment::where('user_id', $employee->id)
                                   ->where('site_id', $site->id)
                                   ->count();
        $this->assertEquals(1, $assignmentCount);

        Log::info('Duplicate assignment test completed successfully');
    }

    /**
     * Test employee can view their assignments.
     */
    public function test_employee_can_view_their_assignments(): void
    {
        Log::info('Testing employee can view their assignments');

        // Create employees and sites
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);
        $site1 = $this->createTestSite(['name' => 'Site 1']);
        $site2 = $this->createTestSite(['name' => 'Site 2']);

        // Create assignments
        $assignment1 = $this->createTestAssignment($employee1, $site1);
        $assignment2 = $this->createTestAssignment($employee1, $site2);
        $assignment3 = $this->createTestAssignment($employee2, $site1); // Different employee

        Sanctum::actingAs($employee1);

        // Request own assignments
        $response = $this->getJson('/api/my-assignments');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => [
                             'id', 'site_id',
                             'site' => ['id', 'name', 'latitude', 'longitude']
                         ]
                     ]
                 ]);

        // Verify only employee1's assignments are in response
        $responseData = $response->json();
        $assignmentIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($assignment1->id, $assignmentIds);
        $this->assertContains($assignment2->id, $assignmentIds);
        $this->assertNotContains($assignment3->id, $assignmentIds);

        // Verify all assignments exist in database
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment1->id, 'user_id' => $employee1->id]);
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment2->id, 'user_id' => $employee1->id]);
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment3->id, 'user_id' => $employee2->id]);

        Log::info('Employee view own assignments test completed successfully', [
            'employee_id' => $employee1->id
        ]);
    }

    /**
     * Test employee cannot access admin assignment endpoints.
     */
    public function test_employee_cannot_access_admin_assignment_endpoints(): void
    {
        Log::info('Testing employee cannot access admin assignment endpoints');

        // Create regular employee
        $employee = $this->createTestUser();
        Sanctum::actingAs($employee);

        // Try to access admin endpoints
        $endpoints = [
            ['method' => 'get', 'url' => '/api/assignments'],
            ['method' => 'post', 'url' => '/api/assignments', 'data' => ['user_id' => 1, 'site_id' => 1]],
            ['method' => 'delete', 'url' => '/api/assignments/1']
        ];

        foreach ($endpoints as $endpoint) {
            $response = match($endpoint['method']) {
                'get' => $this->getJson($endpoint['url']),
                'post' => $this->postJson($endpoint['url'], $endpoint['data'] ?? []),
                'delete' => $this->deleteJson($endpoint['url'])
            };

            // Assert forbidden access
            $response->assertStatus(403);
        }

        Log::info('Employee assignment access restriction test completed successfully');
    }

    /**
     * Test assignment cascade deletion when employee is deleted.
     */
    public function test_assignment_cascade_deletion_when_employee_deleted(): void
    {
        Log::info('Testing assignment cascade deletion when employee deleted');

        // Create admin, employee and site
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        $assignment = $this->createTestAssignment($employee, $site);

        Sanctum::actingAs($admin);

        // Verify assignment exists
        $this->assertDatabaseHasRecord('assignments', [
            'id' => $assignment->id,
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        // Delete employee
        $response = $this->deleteJson("/api/employees/{$employee->id}");
        $response->assertStatus(200);

        // Verify assignment is also deleted (cascade)
        $this->assertDatabaseMissingRecord('assignments', [
            'id' => $assignment->id,
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        // Verify site still exists
        $this->assertDatabaseHasRecord('sites', ['id' => $site->id]);

        Log::info('Assignment cascade deletion test completed successfully', [
            'employee_id' => $employee->id,
            'assignment_id' => $assignment->id
        ]);
    }

    /**
     * Test assignment cascade deletion when site is deleted.
     */
    public function test_assignment_cascade_deletion_when_site_deleted(): void
    {
        Log::info('Testing assignment cascade deletion when site deleted');

        // Create admin, employee and site
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        $assignment = $this->createTestAssignment($employee, $site);

        Sanctum::actingAs($admin);

        // Verify assignment exists
        $this->assertDatabaseHasRecord('assignments', [
            'id' => $assignment->id,
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        // Delete site
        $response = $this->deleteJson("/api/sites/{$site->id}");
        $response->assertStatus(200);

        // Verify assignment is also deleted (cascade)
        $this->assertDatabaseMissingRecord('assignments', [
            'id' => $assignment->id,
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        // Verify employee still exists
        $this->assertDatabaseHasRecord('users', ['id' => $employee->id]);

        Log::info('Assignment cascade deletion test completed successfully', [
            'site_id' => $site->id,
            'assignment_id' => $assignment->id
        ]);
    }
}
