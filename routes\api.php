<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\SiteController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\PointageController;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\VerificationController;
use App\Http\Controllers\AdminController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Routes d'authentification (publiques)
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    
    // Routes protégées par authentification
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('profile', [AuthController::class, 'profile']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::put('change-password', [AuthController::class, 'changePassword']);
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('logout-all', [AuthController::class, 'logoutAll']);
    });
});

// Routes protégées par authentification
Route::middleware('auth:sanctum')->group(function () {
    
    // Routes des sites
    Route::apiResource('sites', SiteController::class);
    Route::get('sites/assigned', [SiteController::class, 'assignedSites']);
    Route::post('sites/{site}/distance', [SiteController::class, 'calculateDistance']);
    Route::get('sites/{site}/statistics', [SiteController::class, 'statistics']);
    
    // Routes des pointages
    Route::prefix('pointages')->group(function () {
        Route::get('/', [PointageController::class, 'index']);
        Route::get('current', [PointageController::class, 'current']);
        Route::get('statistics', [PointageController::class, 'statistics']);
        Route::post('start', [PointageController::class, 'start']);
        Route::post('{pointage}/end', [PointageController::class, 'end']);
        Route::get('{pointage}', [PointageController::class, 'show']);
    });
    
    // Routes des vérifications
    Route::prefix('verifications')->group(function () {
        Route::get('/', [VerificationController::class, 'index']);
        Route::post('/', [VerificationController::class, 'store']);
        Route::get('recent', [VerificationController::class, 'recent']);
        Route::get('statistics', [VerificationController::class, 'statistics']);
        Route::post('auto', [VerificationController::class, 'createAutomatic']);
        Route::get('{verification}', [VerificationController::class, 'show']);
        Route::delete('{verification}', [VerificationController::class, 'destroy']);
        Route::get('{verification1}/distance/{verification2}', [VerificationController::class, 'calculateDistance']);
    });
    
    // Routes des assignments (pour les employés - leurs propres assignments)
    Route::get('assignments/my', [AssignmentController::class, 'myAssignments']);
    
    // Routes d'administration (réservées aux admins)
    Route::middleware('admin')->group(function () {
        
        // Gestion des employés
        Route::apiResource('employees', EmployeeController::class);
        Route::post('employees/{employee}/assign', [EmployeeController::class, 'assignToSite']);
        Route::delete('employees/{employee}/unassign/{site}', [EmployeeController::class, 'unassignFromSite']);
        Route::get('employees/{employee}/sites', [EmployeeController::class, 'assignedSites']);
        Route::get('employees/{employee}/pointages', [EmployeeController::class, 'pointages']);
        Route::get('employees/{employee}/statistics', [EmployeeController::class, 'statistics']);
        Route::put('employees/{employee}/password', [EmployeeController::class, 'changePassword']);
        
        // Gestion des assignments
        Route::apiResource('assignments', AssignmentController::class);
        Route::get('assignments/user/{user}', [AssignmentController::class, 'byUser']);
        Route::get('assignments/site/{site}', [AssignmentController::class, 'bySite']);
        Route::get('assignments/check/{user}/{site}', [AssignmentController::class, 'checkAssignment']);
        Route::get('assignments/statistics', [AssignmentController::class, 'statistics']);
        Route::post('assignments/bulk', [AssignmentController::class, 'bulkCreate']);
        Route::delete('assignments/bulk', [AssignmentController::class, 'bulkDelete']);
        
        // Administration générale
        Route::prefix('admin')->group(function () {
            Route::get('dashboard', [AdminController::class, 'dashboard']);
            Route::get('pointages', [PointageController::class, 'adminIndex']);
            Route::get('pointages/active', [PointageController::class, 'activePointages']);
            Route::get('verifications', [VerificationController::class, 'adminIndex']);
            Route::post('reports/generate', [AdminController::class, 'generateReport']);
        });
    });
});

// Route de test pour vérifier que l'API fonctionne
Route::get('health', function () {
    return response()->json([
        'status' => 'OK',
        'message' => 'ClockIn API is running',
        'timestamp' => now()->toISOString()
    ]);
});
