<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use Tests\Traits\DatabaseTestCase;
use Tests\Traits\AuthenticationHelpers;
use App\Models\User;
use App\Models\Site;
use App\Models\Assignment;
use App\Models\Pointage;
use App\Models\Verification;
use Carbon\Carbon;

class CompleteWorkflowIntegrationTest extends TestCase
{
    use DatabaseTestCase, AuthenticationHelpers;

    /**
     * Test du workflow complet : inscription → assignation → pointage → vérification
     */
    public function test_complete_employee_workflow(): void
    {
        // 1. Créer un admin et un site
        $admin = $this->createTestAdmin();
        $site = Site::factory()->create([
            'name' => 'Chantier Test',
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ]);

        // 2. L'admin crée un employé
        $employeeData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'employee'
        ];

        $response = $this->authenticatedPost('/api/employees', $employeeData, $admin);
        $response->assertStatus(201);
        
        $employee = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($employee);

        // 3. L'admin assigne l'employé au site
        $assignmentData = ['site_id' => $site->id];
        $response = $this->authenticatedPost("/api/employees/{$employee->id}/assign", $assignmentData, $admin);
        $response->assertStatus(201);

        // 4. L'employé se connecte
        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];
        $response = $this->withHeaders($this->getJsonHeaders())
                         ->postJson('/api/auth/login', $loginData);
        $response->assertStatus(200);
        $token = $response->json('data.token');

        // 5. L'employé vérifie ses sites assignés
        $response = $this->withHeaders($this->getAuthHeaders($token))
                         ->getJson('/api/sites/assigned');
        $response->assertStatus(200);
        $assignedSites = $response->json('data');
        $this->assertCount(1, $assignedSites);
        $this->assertEquals($site->id, $assignedSites[0]['id']);

        // 6. L'employé commence un pointage
        $pointageData = [
            'site_id' => $site->id,
            'latitude' => $site->latitude + 0.0001, // Proche du site
            'longitude' => $site->longitude + 0.0001
        ];
        $response = $this->withHeaders($this->getAuthHeaders($token))
                         ->postJson('/api/pointages/start', $pointageData);
        $response->assertStatus(201);
        $pointageId = $response->json('data.pointage.id');

        // 7. Vérifier qu'une vérification automatique a été créée
        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $employee->id
        ]);

        // 8. L'employé vérifie son pointage en cours
        $response = $this->withHeaders($this->getAuthHeaders($token))
                         ->getJson('/api/pointages/current');
        $response->assertStatus(200);
        $currentPointage = $response->json('data.pointage');
        $this->assertEquals($pointageId, $currentPointage['id']);

        // 9. L'employé crée une vérification manuelle
        $verificationData = [
            'latitude' => $site->latitude + 0.0002,
            'longitude' => $site->longitude + 0.0002
        ];
        $response = $this->withHeaders($this->getAuthHeaders($token))
                         ->postJson('/api/verifications', $verificationData);
        $response->assertStatus(201);

        // 10. Simuler du temps de travail (2 heures)
        $pointage = Pointage::find($pointageId);
        $pointage->debut_pointage = Carbon::now()->subHours(2);
        $pointage->save();

        // 11. L'employé termine son pointage
        $endData = [
            'latitude' => $site->latitude + 0.0001,
            'longitude' => $site->longitude + 0.0001
        ];
        $response = $this->withHeaders($this->getAuthHeaders($token))
                         ->postJson("/api/pointages/{$pointageId}/end", $endData);
        $response->assertStatus(200);

        // 12. Vérifier que le pointage est terminé avec la durée calculée
        $pointage->refresh();
        $this->assertNotNull($pointage->fin_pointage);
        $this->assertNotNull($pointage->duree);

        // 13. L'admin consulte les statistiques
        $response = $this->authenticatedGet("/api/employees/{$employee->id}/statistics", $admin);
        $response->assertStatus(200);
        $stats = $response->json('data');
        $this->assertEquals(1, $stats['total_pointages']);

        // 14. L'admin consulte les pointages du site
        $response = $this->authenticatedGet("/api/sites/{$site->id}/statistics", $admin);
        $response->assertStatus(200);
        $siteStats = $response->json('data');
        $this->assertEquals(1, $siteStats['total_employees']);
        $this->assertEquals(1, $siteStats['total_pointages']);

        // 15. Vérifier l'intégrité des données
        $this->assertDatabaseHasRecord('users', ['id' => $employee->id, 'role' => 'employee']);
        $this->assertDatabaseHasRecord('sites', ['id' => $site->id]);
        $this->assertDatabaseHasRecord('assignments', ['user_id' => $employee->id, 'site_id' => $site->id]);
        $this->assertDatabaseHasRecord('pointages', ['id' => $pointageId, 'user_id' => $employee->id]);
        $this->assertTrue(Verification::where('user_id', $employee->id)->count() >= 2);
    }

    /**
     * Test du workflow d'administration complet
     */
    public function test_complete_admin_workflow(): void
    {
        $admin = $this->createTestAdmin();

        // 1. Créer plusieurs sites
        $sites = Site::factory()->count(3)->create();

        // 2. Créer plusieurs employés
        $employees = User::factory()->count(5)->create(['role' => 'employee']);

        // 3. Assigner les employés aux sites
        foreach ($employees as $index => $employee) {
            $site = $sites[$index % 3]; // Distribution circulaire
            Assignment::factory()->forUserAndSite($employee, $site)->create();
        }

        // 4. Simuler des pointages
        foreach ($employees as $employee) {
            $assignedSite = $employee->assignments()->first()->site;
            
            // Pointage terminé
            Pointage::factory()
                   ->forUser($employee)
                   ->forSite($assignedSite)
                   ->termine()
                   ->create();
            
            // Pointage en cours
            Pointage::factory()
                   ->forUser($employee)
                   ->forSite($assignedSite)
                   ->enCours()
                   ->create();
        }

        // 5. L'admin consulte le tableau de bord
        $response = $this->authenticatedGet('/api/admin/dashboard', $admin);
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_employees',
                        'total_sites',
                        'active_pointages',
                        'total_pointages_today'
                    ]
                ]);

        $dashboard = $response->json('data');
        $this->assertEquals(5, $dashboard['total_employees']);
        $this->assertEquals(3, $dashboard['total_sites']);
        $this->assertEquals(5, $dashboard['active_pointages']);

        // 6. L'admin consulte tous les pointages actifs
        $response = $this->authenticatedGet('/api/admin/pointages/active', $admin);
        $response->assertStatus(200);
        $activePointages = $response->json('data');
        $this->assertCount(5, $activePointages);

        // 7. L'admin génère un rapport
        $reportData = [
            'start_date' => Carbon::today()->format('Y-m-d'),
            'end_date' => Carbon::today()->format('Y-m-d'),
            'include_employees' => true,
            'include_sites' => true,
            'include_pointages' => true
        ];

        $response = $this->authenticatedPost('/api/admin/reports/generate', $reportData, $admin);
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'report' => [
                            'period',
                            'employees_summary',
                            'sites_summary',
                            'pointages_summary'
                        ]
                    ]
                ]);
    }

    /**
     * Test de gestion des erreurs et cas limites
     */
    public function test_error_handling_and_edge_cases(): void
    {
        $employee = $this->createTestUser();
        $site = Site::factory()->create();

        // 1. Tentative de pointage sans assignment
        $pointageData = [
            'site_id' => $site->id,
            'latitude' => $site->latitude,
            'longitude' => $site->longitude
        ];

        $response = $this->authenticatedPost('/api/pointages/start', $pointageData, $employee);
        $response->assertStatus(403);

        // 2. Assigner l'employé au site
        Assignment::factory()->forUserAndSite($employee, $site)->create();

        // 3. Pointage trop éloigné du site
        $pointageData['latitude'] = $site->latitude + 0.01; // ~1km
        $response = $this->authenticatedPost('/api/pointages/start', $pointageData, $employee);
        $response->assertStatus(400);

        // 4. Pointage valide
        $pointageData['latitude'] = $site->latitude + 0.0001; // ~10m
        $response = $this->authenticatedPost('/api/pointages/start', $pointageData, $employee);
        $response->assertStatus(201);

        // 5. Tentative de second pointage
        $response = $this->authenticatedPost('/api/pointages/start', $pointageData, $employee);
        $response->assertStatus(400);

        // 6. Terminer le pointage
        $pointage = Pointage::where('user_id', $employee->id)->first();
        $endData = [
            'latitude' => $site->latitude,
            'longitude' => $site->longitude
        ];
        $response = $this->authenticatedPost("/api/pointages/{$pointage->id}/end", $endData, $employee);
        $response->assertStatus(200);

        // 7. Tentative de terminer à nouveau
        $response = $this->authenticatedPost("/api/pointages/{$pointage->id}/end", $endData, $employee);
        $response->assertStatus(400);
    }

    /**
     * Test de performance avec de nombreuses données
     */
    public function test_performance_with_large_dataset(): void
    {
        $admin = $this->createTestAdmin();
        
        $startTime = microtime(true);

        // Créer un dataset important
        $sites = Site::factory()->count(10)->create();
        $employees = User::factory()->count(50)->create(['role' => 'employee']);

        // Créer des assignments
        foreach ($employees as $employee) {
            $randomSites = $sites->random(rand(1, 3));
            foreach ($randomSites as $site) {
                Assignment::factory()->forUserAndSite($employee, $site)->create();
            }
        }

        // Créer des pointages et vérifications
        foreach ($employees as $employee) {
            $assignments = $employee->assignments;
            foreach ($assignments as $assignment) {
                // Pointages récents
                Pointage::factory()
                       ->forUser($employee)
                       ->forSite($assignment->site)
                       ->count(rand(1, 5))
                       ->create();
                
                // Vérifications
                Verification::factory()
                           ->forUser($employee)
                           ->count(rand(2, 8))
                           ->create();
            }
        }

        $creationTime = microtime(true) - $startTime;

        // Test de performance des requêtes
        $queryStartTime = microtime(true);

        // Requêtes complexes
        $response = $this->authenticatedGet('/api/admin/dashboard', $admin);
        $response->assertStatus(200);

        $response = $this->authenticatedGet('/api/admin/pointages', $admin);
        $response->assertStatus(200);

        $response = $this->authenticatedGet('/api/assignments/statistics', $admin);
        $response->assertStatus(200);

        $queryTime = microtime(true) - $queryStartTime;

        // Vérifications de performance
        $this->assertLessThan(10.0, $creationTime, "Création des données trop lente: {$creationTime}s");
        $this->assertLessThan(2.0, $queryTime, "Requêtes trop lentes: {$queryTime}s");

        // Vérifier les comptes
        $this->assertEquals(50, User::where('role', 'employee')->count());
        $this->assertEquals(10, Site::count());
        $this->assertGreaterThan(50, Assignment::count());
        $this->assertGreaterThan(50, Pointage::count());
        $this->assertGreaterThan(100, Verification::count());
    }
}
