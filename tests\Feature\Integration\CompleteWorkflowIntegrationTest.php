<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use Tests\Traits\DatabaseIntegrationTrait;
use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Assignment;
use App\Models\Verification;
use App\Models\Log;
use Illuminate\Support\Facades\Log as LaravelLog;
use Laravel\Sanctum\Sanctum;
use Carbon\Carbon;

class CompleteWorkflowIntegrationTest extends TestCase
{
    use DatabaseIntegrationTrait;

    /**
     * Test complete employee workflow from registration to pointage.
     */
    public function test_complete_employee_workflow(): void
    {
        LaravelLog::info('Testing complete employee workflow');

        // Step 1: Admin creates employee
        $admin = $this->createTestAdmin();
        Sanctum::actingAs($admin);

        $employeeData = [
            'name' => 'Workflow Employee',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'employee'
        ];

        $response = $this->postJson('/api/employees', $employeeData);
        $response->assertStatus(201);

        $employee = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($employee);

        // Verify employee is in database
        $this->assertDatabaseHasRecord('users', [
            'id' => $employee->id,
            'email' => '<EMAIL>',
            'role' => 'employee'
        ]);

        // Step 2: Admin creates site
        $siteData = [
            'name' => 'Workflow Site',
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ];

        $response = $this->postJson('/api/sites', $siteData);
        $response->assertStatus(201);

        $site = Site::where('name', 'Workflow Site')->first();
        $this->assertNotNull($site);

        // Verify site is in database
        $this->assertDatabaseHasRecord('sites', [
            'id' => $site->id,
            'name' => 'Workflow Site'
        ]);

        // Step 3: Admin assigns employee to site
        $assignmentData = [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ];

        $response = $this->postJson('/api/assignments', $assignmentData);
        $response->assertStatus(201);

        // Verify assignment is in database
        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        // Step 4: Employee logs in
        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->postJson('/api/login', $loginData);
        $response->assertStatus(200);

        $token = $response->json('token');
        $this->assertNotEmpty($token);

        // Switch to employee authentication
        Sanctum::actingAs($employee);

        // Step 5: Employee creates verification
        $verificationData = [
            'latitude' => 33.8869,
            'longitude' => -9.5375,
            'date_heure' => Carbon::now()->toISOString()
        ];

        $response = $this->postJson('/api/verifications', $verificationData);
        $response->assertStatus(201);

        // Verify verification is in database
        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $employee->id,
            'latitude' => 33.8869,
            'longitude' => -9.5375
        ]);

        // Step 6: Employee starts pointage
        $pointageStartData = [
            'site_id' => $site->id,
            'debut_latitude' => 33.8869,
            'debut_longitude' => -9.5375
        ];

        $response = $this->postJson('/api/pointages/start', $pointageStartData);
        $response->assertStatus(201);

        $pointage = Pointage::where('user_id', $employee->id)->first();
        $this->assertNotNull($pointage);

        // Verify pointage is in database
        $this->assertDatabaseHasRecord('pointages', [
            'id' => $pointage->id,
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);

        // Step 7: Employee ends pointage after some time
        $pointageEndData = [
            'fin_latitude' => 33.8870,
            'fin_longitude' => -9.5376
        ];

        $response = $this->putJson("/api/pointages/{$pointage->id}/end", $pointageEndData);
        $response->assertStatus(200);

        // Verify pointage is updated in database
        $this->assertDatabaseHasRecord('pointages', [
            'id' => $pointage->id,
            'fin_latitude' => 33.8870,
            'fin_longitude' => -9.5376
        ]);

        $updatedPointage = Pointage::find($pointage->id);
        $this->assertNotNull($updatedPointage->fin_pointage);
        $this->assertNotNull($updatedPointage->duree);

        // Step 8: Verify logs were created for all actions
        $this->assertDatabaseHas('logs', [
            'user_id' => $employee->id,
            'action' => 'pointage_start'
        ]);

        $this->assertDatabaseHas('logs', [
            'user_id' => $employee->id,
            'action' => 'pointage_end'
        ]);

        // Step 9: Admin views reports
        Sanctum::actingAs($admin);

        // View all pointages
        $response = $this->getJson('/api/admin/pointages');
        $response->assertStatus(200);

        $pointageIds = collect($response->json('data'))->pluck('id')->toArray();
        $this->assertContains($pointage->id, $pointageIds);

        // View all verifications
        $response = $this->getJson('/api/admin/verifications');
        $response->assertStatus(200);

        $verificationIds = collect($response->json('data'))->pluck('user_id')->toArray();
        $this->assertContains($employee->id, $verificationIds);

        // View all logs
        $response = $this->getJson('/api/admin/logs');
        $response->assertStatus(200);

        $logUserIds = collect($response->json('data'))->pluck('user_id')->toArray();
        $this->assertContains($employee->id, $logUserIds);

        // Verify all data integrity
        $this->assertEquals(1, User::where('email', '<EMAIL>')->count());
        $this->assertEquals(1, Site::where('name', 'Workflow Site')->count());
        $this->assertEquals(1, Assignment::where('user_id', $employee->id)->where('site_id', $site->id)->count());
        $this->assertEquals(1, Pointage::where('user_id', $employee->id)->count());
        $this->assertGreaterThanOrEqual(1, Verification::where('user_id', $employee->id)->count());
        $this->assertGreaterThanOrEqual(2, Log::where('user_id', $employee->id)->count());

        LaravelLog::info('Complete employee workflow test completed successfully', [
            'employee_id' => $employee->id,
            'site_id' => $site->id,
            'pointage_id' => $pointage->id
        ]);
    }

    /**
     * Test data consistency across all tables.
     */
    public function test_data_consistency_across_all_tables(): void
    {
        LaravelLog::info('Testing data consistency across all tables');

        // Create complete dataset
        $admin = $this->createTestAdmin();
        $employee1 = $this->createTestUser(['name' => 'Employee 1']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2']);
        $site1 = $this->createTestSite(['name' => 'Site 1']);
        $site2 = $this->createTestSite(['name' => 'Site 2']);

        // Create assignments
        $assignment1 = $this->createTestAssignment($employee1, $site1);
        $assignment2 = $this->createTestAssignment($employee2, $site2);

        // Create pointages
        $pointage1 = $this->createTestPointage($employee1, $site1);
        $pointage2 = $this->createTestPointage($employee2, $site2);

        // Create verifications
        $verification1 = $this->createTestVerification($employee1);
        $verification2 = $this->createTestVerification($employee2);

        // Verify all relationships are consistent
        
        // Check user-assignment relationships
        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $employee1->id,
            'site_id' => $site1->id
        ]);
        
        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $employee2->id,
            'site_id' => $site2->id
        ]);

        // Check user-pointage relationships
        $this->assertDatabaseHasRecord('pointages', [
            'user_id' => $employee1->id,
            'site_id' => $site1->id
        ]);
        
        $this->assertDatabaseHasRecord('pointages', [
            'user_id' => $employee2->id,
            'site_id' => $site2->id
        ]);

        // Check user-verification relationships
        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $employee1->id
        ]);
        
        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $employee2->id
        ]);

        // Verify foreign key constraints work
        $userCount = User::count();
        $siteCount = Site::count();
        $assignmentCount = Assignment::count();
        $pointageCount = Pointage::count();
        $verificationCount = Verification::count();

        $this->assertGreaterThanOrEqual(3, $userCount); // admin + 2 employees
        $this->assertEquals(2, $siteCount);
        $this->assertEquals(2, $assignmentCount);
        $this->assertEquals(2, $pointageCount);
        $this->assertEquals(2, $verificationCount);

        LaravelLog::info('Data consistency test completed successfully', [
            'users' => $userCount,
            'sites' => $siteCount,
            'assignments' => $assignmentCount,
            'pointages' => $pointageCount,
            'verifications' => $verificationCount
        ]);
    }

    /**
     * Test cascade deletion behavior.
     */
    public function test_cascade_deletion_behavior(): void
    {
        LaravelLog::info('Testing cascade deletion behavior');

        // Create admin and test data
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser();
        $site = $this->createTestSite();
        
        $assignment = $this->createTestAssignment($employee, $site);
        $pointage = $this->createTestPointage($employee, $site);
        $verification = $this->createTestVerification($employee);

        // Verify all data exists
        $this->assertDatabaseHasRecord('users', ['id' => $employee->id]);
        $this->assertDatabaseHasRecord('sites', ['id' => $site->id]);
        $this->assertDatabaseHasRecord('assignments', ['id' => $assignment->id]);
        $this->assertDatabaseHasRecord('pointages', ['id' => $pointage->id]);
        $this->assertDatabaseHasRecord('verifications', ['id' => $verification->id]);

        Sanctum::actingAs($admin);

        // Delete employee - should cascade to related records
        $response = $this->deleteJson("/api/employees/{$employee->id}");
        $response->assertStatus(200);

        // Verify employee and related records are deleted
        $this->assertDatabaseMissingRecord('users', ['id' => $employee->id]);
        $this->assertDatabaseMissingRecord('assignments', ['id' => $assignment->id]);
        $this->assertDatabaseMissingRecord('pointages', ['id' => $pointage->id]);
        $this->assertDatabaseMissingRecord('verifications', ['id' => $verification->id]);

        // Verify site still exists (should not be deleted)
        $this->assertDatabaseHasRecord('sites', ['id' => $site->id]);

        LaravelLog::info('Cascade deletion test completed successfully');
    }

    /**
     * Test database transaction integrity.
     */
    public function test_database_transaction_integrity(): void
    {
        LaravelLog::info('Testing database transaction integrity');

        $initialUserCount = User::count();
        $initialSiteCount = Site::count();
        $initialAssignmentCount = Assignment::count();

        // Create admin
        $admin = $this->createTestAdmin();
        Sanctum::actingAs($admin);

        // Attempt to create employee with invalid data (should rollback)
        $invalidEmployeeData = [
            'name' => 'Invalid Employee',
            'email' => 'invalid-email', // Invalid email format
            'password' => 'password123',
            'password_confirmation' => 'different_password', // Mismatched passwords
            'role' => 'employee'
        ];

        $response = $this->postJson('/api/employees', $invalidEmployeeData);
        $response->assertStatus(422);

        // Verify no employee was created
        $this->assertEquals($initialUserCount + 1, User::count()); // Only admin was created

        // Create valid employee and site
        $employee = $this->createTestUser();
        $site = $this->createTestSite();

        // Attempt to create duplicate assignment (should fail)
        $this->createTestAssignment($employee, $site);
        
        $duplicateAssignmentData = [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ];

        $response = $this->postJson('/api/assignments', $duplicateAssignmentData);
        $response->assertStatus(422);

        // Verify only one assignment exists
        $this->assertEquals($initialAssignmentCount + 1, Assignment::count());

        LaravelLog::info('Database transaction integrity test completed successfully');
    }

    /**
     * Test performance with multiple concurrent operations.
     */
    public function test_performance_with_multiple_operations(): void
    {
        LaravelLog::info('Testing performance with multiple operations');

        $startTime = microtime(true);

        // Create admin
        $admin = $this->createTestAdmin();
        Sanctum::actingAs($admin);

        // Create multiple employees, sites, and assignments
        $employees = [];
        $sites = [];
        
        for ($i = 1; $i <= 10; $i++) {
            $employees[] = $this->createTestUser([
                'name' => "Performance Employee {$i}",
                'email' => "perf{$i}@example.com"
            ]);
            
            $sites[] = $this->createTestSite([
                'name' => "Performance Site {$i}",
                'latitude' => 33.8869 + ($i * 0.001),
                'longitude' => -9.5375 + ($i * 0.001)
            ]);
        }

        // Create assignments and pointages
        foreach ($employees as $index => $employee) {
            $site = $sites[$index];
            $this->createTestAssignment($employee, $site);
            $this->createTestPointage($employee, $site);
            $this->createTestVerification($employee);
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Verify all data was created
        $this->assertEquals(10, count($employees));
        $this->assertEquals(10, count($sites));
        $this->assertEquals(10, Assignment::whereIn('user_id', collect($employees)->pluck('id'))->count());
        $this->assertEquals(10, Pointage::whereIn('user_id', collect($employees)->pluck('id'))->count());
        $this->assertEquals(10, Verification::whereIn('user_id', collect($employees)->pluck('id'))->count());

        // Performance should be reasonable (less than 30 seconds for 10 records)
        $this->assertLessThan(30, $executionTime);

        LaravelLog::info('Performance test completed successfully', [
            'execution_time' => $executionTime,
            'records_created' => 50 // 10 users + 10 sites + 10 assignments + 10 pointages + 10 verifications
        ]);
    }
}
