{"info": {"_postman_id": "clockin-api-collection", "name": "ClockIn API Collection", "description": "Collection complète pour tester toutes les APIs ClockIn avec Laravel 11 et Sanctum", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}}}, {"name": "Register Employee", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('employee_token', response.data.token);", "    pm.environment.set('employee_id', response.data.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"role\": \"employee\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}}, {"name": "Register Admin", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('admin_token', response.data.token);", "    pm.environment.set('admin_id', response.data.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Admin User\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\",\n    \"role\": \"admin\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}}}, {"name": "<PERSON><PERSON> Employee", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('employee_token', response.data.token);", "    pm.environment.set('employee_id', response.data.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('admin_token', response.data.token);", "    pm.environment.set('admin_id', response.data.user.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}], "url": {"raw": "{{base_url}}/api/auth/profile", "host": ["{{base_url}}"], "path": ["api", "auth", "profile"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}], "url": {"raw": "{{base_url}}/api/auth/logout", "host": ["{{base_url}}"], "path": ["api", "auth", "logout"]}}}]}, {"name": "Sites Management", "item": [{"name": "Create Site (Admin)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('site_id', response.data.site.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Chantier Test Paris\",\n    \"latitude\": 48.8566,\n    \"longitude\": 2.3522\n}"}, "url": {"raw": "{{base_url}}/api/sites", "host": ["{{base_url}}"], "path": ["api", "sites"]}}}, {"name": "Get All Sites", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}], "url": {"raw": "{{base_url}}/api/sites", "host": ["{{base_url}}"], "path": ["api", "sites"]}}}, {"name": "Get Site by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}], "url": {"raw": "{{base_url}}/api/sites/{{site_id}}", "host": ["{{base_url}}"], "path": ["api", "sites", "{{site_id}}"]}}}, {"name": "Update Site (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Chantier Test Paris - Modifié\",\n    \"latitude\": 48.8576,\n    \"longitude\": 2.3532\n}"}, "url": {"raw": "{{base_url}}/api/sites/{{site_id}}", "host": ["{{base_url}}"], "path": ["api", "sites", "{{site_id}}"]}}}, {"name": "Calculate Distance from Site", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 48.8576,\n    \"longitude\": 2.3532\n}"}, "url": {"raw": "{{base_url}}/api/sites/{{site_id}}/distance", "host": ["{{base_url}}"], "path": ["api", "sites", "{{site_id}}", "distance"]}}}]}, {"name": "Employee Management", "item": [{"name": "Get All Employees (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/employees", "host": ["{{base_url}}"], "path": ["api", "employees"]}}}, {"name": "Create Employee (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/employees", "host": ["{{base_url}}"], "path": ["api", "employees"]}}}, {"name": "Assign Employee to Site (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"site_id\": {{site_id}}\n}"}, "url": {"raw": "{{base_url}}/api/employees/{{employee_id}}/assign", "host": ["{{base_url}}"], "path": ["api", "employees", "{{employee_id}}", "assign"]}}}]}, {"name": "Pointage Management", "item": [{"name": "Start Pointage", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.environment.set('pointage_id', response.data.pointage.id);", "}"]}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"site_id\": {{site_id}},\n    \"latitude\": 48.8566,\n    \"longitude\": 2.3522\n}"}, "url": {"raw": "{{base_url}}/api/pointages/start", "host": ["{{base_url}}"], "path": ["api", "pointages", "start"]}}}, {"name": "Get Current Pointage", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}], "url": {"raw": "{{base_url}}/api/pointages/current", "host": ["{{base_url}}"], "path": ["api", "pointages", "current"]}}}, {"name": "End Pointage", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 48.8576,\n    \"longitude\": 2.3532\n}"}, "url": {"raw": "{{base_url}}/api/pointages/{{pointage_id}}/end", "host": ["{{base_url}}"], "path": ["api", "pointages", "{{pointage_id}}", "end"]}}}, {"name": "Get My Pointages", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}], "url": {"raw": "{{base_url}}/api/pointages", "host": ["{{base_url}}"], "path": ["api", "pointages"]}}}, {"name": "Get All Pointages (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/admin/pointages", "host": ["{{base_url}}"], "path": ["api", "admin", "pointages"]}}}]}, {"name": "Verification Management", "item": [{"name": "Create Verification", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 48.8566,\n    \"longitude\": 2.3522\n}"}, "url": {"raw": "{{base_url}}/api/verifications", "host": ["{{base_url}}"], "path": ["api", "verifications"]}}}, {"name": "Get My Verifications", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}], "url": {"raw": "{{base_url}}/api/verifications", "host": ["{{base_url}}"], "path": ["api", "verifications"]}}}, {"name": "Get Verification Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{employee_token}}"}], "url": {"raw": "{{base_url}}/api/verifications/statistics", "host": ["{{base_url}}"], "path": ["api", "verifications", "statistics"]}}}]}, {"name": "Admin Dashboard", "item": [{"name": "Get Dashboard", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/api/admin/dashboard", "host": ["{{base_url}}"], "path": ["api", "admin", "dashboard"]}}}, {"name": "Generate Report", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{admin_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"start_date\": \"2024-01-01\",\n    \"end_date\": \"2024-12-31\",\n    \"include_employees\": true,\n    \"include_sites\": true,\n    \"include_pointages\": true\n}"}, "url": {"raw": "{{base_url}}/api/admin/reports/generate", "host": ["{{base_url}}"], "path": ["api", "admin", "reports", "generate"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8000"}]}