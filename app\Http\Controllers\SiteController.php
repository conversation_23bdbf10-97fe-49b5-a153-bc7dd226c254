<?php

namespace App\Http\Controllers;

use App\Models\Site;
use App\Models\Assignment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SiteController extends Controller
{
    /**
     * Liste de tous les sites
     */
    public function index(Request $request): JsonResponse
    {
        $query = Site::query();

        // Recherche par nom
        if ($request->has('search')) {
            $query->where('name', 'LIKE', '%' . $request->search . '%');
        }

        $sites = $query->get();

        return response()->json([
            'success' => true,
            'data' => $sites
        ]);
    }

    /**
     * Créer un nouveau site (admin uniquement)
     */
    public function store(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $site = Site::create($request->only(['name', 'latitude', 'longitude']));

        return response()->json([
            'success' => true,
            'data' => [
                'site' => $site
            ],
            'message' => 'Site créé avec succès'
        ], 201);
    }

    /**
     * Afficher un site spécifique
     */
    public function show(Site $site): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'site' => $site
            ]
        ]);
    }

    /**
     * Mettre à jour un site (admin uniquement)
     */
    public function update(Request $request, Site $site): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'latitude' => 'sometimes|numeric|between:-90,90',
            'longitude' => 'sometimes|numeric|between:-180,180'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $site->update($request->only(['name', 'latitude', 'longitude']));

        return response()->json([
            'success' => true,
            'data' => [
                'site' => $site->fresh()
            ],
            'message' => 'Site mis à jour avec succès'
        ]);
    }

    /**
     * Supprimer un site (admin uniquement)
     */
    public function destroy(Request $request, Site $site): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $site->delete();

        return response()->json([
            'success' => true,
            'message' => 'Site supprimé avec succès'
        ]);
    }

    /**
     * Sites assignés à l'utilisateur connecté
     */
    public function assignedSites(Request $request): JsonResponse
    {
        $user = $request->user();
        $sites = $user->assignments()->with('site')->get()->pluck('site');

        return response()->json([
            'success' => true,
            'data' => $sites
        ]);
    }

    /**
     * Calculer la distance depuis un site
     */
    public function calculateDistance(Request $request, Site $site): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $distance = $site->distanceFrom($request->latitude, $request->longitude);
        $withinRange = $distance <= 100; // 100 mètres de tolérance

        return response()->json([
            'success' => true,
            'data' => [
                'distance_meters' => $distance,
                'within_range' => $withinRange
            ]
        ]);
    }

    /**
     * Statistiques d'un site (admin uniquement)
     */
    public function statistics(Request $request, Site $site): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $totalEmployees = $site->assignments()->count();
        $activePointages = $site->pointages()->whereNull('fin_pointage')->count();
        $totalPointages = $site->pointages()->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_employees' => $totalEmployees,
                'active_pointages' => $activePointages,
                'total_pointages' => $totalPointages,
                'average_duration' => null // À implémenter si nécessaire
            ]
        ]);
    }
}
