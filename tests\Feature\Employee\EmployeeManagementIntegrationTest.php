<?php

namespace Tests\Feature\Employee;

use Tests\TestCase;
use Tests\Traits\DatabaseIntegrationTrait;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\Sanctum;

class EmployeeManagementIntegrationTest extends TestCase
{
    use DatabaseIntegrationTrait;

    /**
     * Test admin can list all employees.
     */
    public function test_admin_can_list_all_employees(): void
    {
        Log::info('Testing admin can list all employees');

        // Create admin user
        $admin = $this->createTestAdmin();
        Sanctum::actingAs($admin);

        // Create multiple employees
        $employee1 = $this->createTestUser(['name' => 'Employee 1', 'email' => '<EMAIL>']);
        $employee2 = $this->createTestUser(['name' => 'Employee 2', 'email' => '<EMAIL>']);
        $employee3 = $this->createTestUser(['name' => 'Employee 3', 'email' => '<EMAIL>']);

        // Request employees list
        $response = $this->getJson('/api/employees');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'data' => [
                         '*' => ['id', 'name', 'email', 'role', 'created_at', 'updated_at']
                     ]
                 ]);

        // Verify all employees are in response
        $responseData = $response->json();
        $employeeIds = collect($responseData['data'])->pluck('id')->toArray();
        
        $this->assertContains($employee1->id, $employeeIds);
        $this->assertContains($employee2->id, $employeeIds);
        $this->assertContains($employee3->id, $employeeIds);

        // Verify data is from database
        $this->assertDatabaseHasRecord('users', ['id' => $employee1->id]);
        $this->assertDatabaseHasRecord('users', ['id' => $employee2->id]);
        $this->assertDatabaseHasRecord('users', ['id' => $employee3->id]);

        Log::info('Admin list employees test completed successfully');
    }

    /**
     * Test admin can view specific employee details.
     */
    public function test_admin_can_view_employee_details(): void
    {
        Log::info('Testing admin can view employee details');

        // Create admin and employee
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser([
            'name' => 'John Employee',
            'email' => '<EMAIL>'
        ]);
        Sanctum::actingAs($admin);

        // Request employee details
        $response = $this->getJson("/api/employees/{$employee->id}");

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJson([
                     'id' => $employee->id,
                     'name' => 'John Employee',
                     'email' => '<EMAIL>',
                     'role' => 'employee'
                 ]);

        // Verify employee exists in database
        $this->assertDatabaseHasRecord('users', [
            'id' => $employee->id,
            'name' => 'John Employee',
            'email' => '<EMAIL>'
        ]);

        Log::info('Admin view employee details test completed successfully', ['employee_id' => $employee->id]);
    }

    /**
     * Test admin can create new employee.
     */
    public function test_admin_can_create_new_employee(): void
    {
        Log::info('Testing admin can create new employee');

        // Create admin user
        $admin = $this->createTestAdmin();
        Sanctum::actingAs($admin);

        $employeeData = [
            'name' => 'New Employee',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'employee'
        ];

        // Create employee
        $response = $this->postJson('/api/employees', $employeeData);

        // Assert successful creation
        $response->assertStatus(201)
                 ->assertJsonStructure([
                     'id', 'name', 'email', 'role', 'created_at', 'updated_at'
                 ])
                 ->assertJson([
                     'name' => 'New Employee',
                     'email' => '<EMAIL>',
                     'role' => 'employee'
                 ]);

        // Verify employee is stored in database
        $this->assertDatabaseHasRecord('users', [
            'name' => 'New Employee',
            'email' => '<EMAIL>',
            'role' => 'employee'
        ]);

        // Verify password is hashed
        $createdEmployee = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($createdEmployee);
        $this->assertNotEquals('password123', $createdEmployee->password);

        Log::info('Admin create employee test completed successfully', ['employee_id' => $createdEmployee->id]);
    }

    /**
     * Test admin can update employee information.
     */
    public function test_admin_can_update_employee_information(): void
    {
        Log::info('Testing admin can update employee information');

        // Create admin and employee
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser([
            'name' => 'Original Name',
            'email' => '<EMAIL>'
        ]);
        Sanctum::actingAs($admin);

        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>'
        ];

        // Update employee
        $response = $this->putJson("/api/employees/{$employee->id}", $updateData);

        // Assert successful update
        $response->assertStatus(200)
                 ->assertJson([
                     'id' => $employee->id,
                     'name' => 'Updated Name',
                     'email' => '<EMAIL>'
                 ]);

        // Verify changes in database
        $this->assertDatabaseHasRecord('users', [
            'id' => $employee->id,
            'name' => 'Updated Name',
            'email' => '<EMAIL>'
        ]);

        // Verify old data is no longer in database
        $this->assertDatabaseMissingRecord('users', [
            'id' => $employee->id,
            'name' => 'Original Name',
            'email' => '<EMAIL>'
        ]);

        Log::info('Admin update employee test completed successfully', ['employee_id' => $employee->id]);
    }

    /**
     * Test admin can delete employee.
     */
    public function test_admin_can_delete_employee(): void
    {
        Log::info('Testing admin can delete employee');

        // Create admin and employee
        $admin = $this->createTestAdmin();
        $employee = $this->createTestUser([
            'name' => 'To Be Deleted',
            'email' => '<EMAIL>'
        ]);
        Sanctum::actingAs($admin);

        // Verify employee exists before deletion
        $this->assertDatabaseHasRecord('users', [
            'id' => $employee->id,
            'email' => '<EMAIL>'
        ]);

        // Delete employee
        $response = $this->deleteJson("/api/employees/{$employee->id}");

        // Assert successful deletion
        $response->assertStatus(200)
                 ->assertJson([
                     'message' => 'Employee deleted successfully'
                 ]);

        // Verify employee is removed from database
        $this->assertDatabaseMissingRecord('users', [
            'id' => $employee->id,
            'email' => '<EMAIL>'
        ]);

        Log::info('Admin delete employee test completed successfully', ['employee_id' => $employee->id]);
    }

    /**
     * Test employee cannot access admin endpoints.
     */
    public function test_employee_cannot_access_admin_endpoints(): void
    {
        Log::info('Testing employee cannot access admin endpoints');

        // Create regular employee
        $employee = $this->createTestUser();
        Sanctum::actingAs($employee);

        // Try to access admin endpoints
        $endpoints = [
            ['method' => 'get', 'url' => '/api/employees'],
            ['method' => 'post', 'url' => '/api/employees', 'data' => ['name' => 'Test', 'email' => '<EMAIL>']],
            ['method' => 'put', 'url' => '/api/employees/1', 'data' => ['name' => 'Updated']],
            ['method' => 'delete', 'url' => '/api/employees/1']
        ];

        foreach ($endpoints as $endpoint) {
            $response = match($endpoint['method']) {
                'get' => $this->getJson($endpoint['url']),
                'post' => $this->postJson($endpoint['url'], $endpoint['data'] ?? []),
                'put' => $this->putJson($endpoint['url'], $endpoint['data'] ?? []),
                'delete' => $this->deleteJson($endpoint['url'])
            };

            // Assert forbidden access
            $response->assertStatus(403);
        }

        Log::info('Employee access restriction test completed successfully');
    }

    /**
     * Test employee can view their own profile.
     */
    public function test_employee_can_view_own_profile(): void
    {
        Log::info('Testing employee can view own profile');

        // Create employee
        $employee = $this->createTestUser([
            'name' => 'Self Employee',
            'email' => '<EMAIL>'
        ]);
        Sanctum::actingAs($employee);

        // Request own profile
        $response = $this->getJson('/api/profile');

        // Assert successful response
        $response->assertStatus(200)
                 ->assertJson([
                     'id' => $employee->id,
                     'name' => 'Self Employee',
                     'email' => '<EMAIL>',
                     'role' => 'employee'
                 ]);

        // Verify data comes from database
        $this->assertDatabaseHasRecord('users', [
            'id' => $employee->id,
            'name' => 'Self Employee',
            'email' => '<EMAIL>'
        ]);

        Log::info('Employee view own profile test completed successfully', ['employee_id' => $employee->id]);
    }

    /**
     * Test employee can update their own profile.
     */
    public function test_employee_can_update_own_profile(): void
    {
        Log::info('Testing employee can update own profile');

        // Create employee
        $employee = $this->createTestUser([
            'name' => 'Original Employee',
            'email' => '<EMAIL>'
        ]);
        Sanctum::actingAs($employee);

        $updateData = [
            'name' => 'Updated Employee Name'
        ];

        // Update own profile
        $response = $this->putJson('/api/profile', $updateData);

        // Assert successful update
        $response->assertStatus(200)
                 ->assertJson([
                     'id' => $employee->id,
                     'name' => 'Updated Employee Name',
                     'email' => '<EMAIL>'
                 ]);

        // Verify changes in database
        $this->assertDatabaseHasRecord('users', [
            'id' => $employee->id,
            'name' => 'Updated Employee Name',
            'email' => '<EMAIL>'
        ]);

        Log::info('Employee update own profile test completed successfully', ['employee_id' => $employee->id]);
    }
}
