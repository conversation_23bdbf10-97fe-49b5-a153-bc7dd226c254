<?php

/**
 * Database Integration Tests Runner
 * 
 * This script runs all database integration tests for the ClockIn application
 * and provides a comprehensive report of the test results.
 * 
 * Usage: php tests/run_integration_tests.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\Log;

class IntegrationTestRunner
{
    private array $testSuites = [
        'Database Setup' => 'Tests\\Feature\\DatabaseIntegrationTestSuite',
        'Authentication' => 'Tests\\Feature\\Auth\\AuthenticationIntegrationTest',
        'Employee Management' => 'Tests\\Feature\\Employee\\EmployeeManagementIntegrationTest',
        'Site Management' => 'Tests\\Feature\\Site\\SiteManagementIntegrationTest',
        'Pointage System' => 'Tests\\Feature\\Pointage\\PointageIntegrationTest',
        'Verification System' => 'Tests\\Feature\\Verification\\VerificationIntegrationTest',
        'Assignment System' => 'Tests\\Feature\\Assignment\\AssignmentIntegrationTest',
        'Logging System' => 'Tests\\Feature\\Log\\LogIntegrationTest',
        'Complete Workflow' => 'Tests\\Feature\\Integration\\CompleteWorkflowIntegrationTest'
    ];

    private array $results = [];
    private int $totalTests = 0;
    private int $passedTests = 0;
    private int $failedTests = 0;

    public function run(): void
    {
        $this->printHeader();
        $this->checkPrerequisites();
        $this->runAllTests();
        $this->printSummary();
    }

    private function printHeader(): void
    {
        echo "\n";
        echo "================================================================================\n";
        echo "                    CLOCKIN DATABASE INTEGRATION TESTS                         \n";
        echo "================================================================================\n";
        echo "Database: MySQL (clockin_db)\n";
        echo "URL: http://localhost:8080/phpmyadmin/index.php?route=/database/structure&db=clockin_db\n";
        echo "Started: " . date('Y-m-d H:i:s') . "\n";
        echo "================================================================================\n\n";
    }

    private function checkPrerequisites(): void
    {
        echo "🔍 Checking prerequisites...\n";

        // Check if Laravel is available
        if (!class_exists('Illuminate\\Foundation\\Application')) {
            $this->fail("Laravel framework not found. Please run 'composer install'.");
        }

        // Check database connection
        try {
            $app = require __DIR__ . '/../bootstrap/app.php';
            $app->make('Illuminate\\Contracts\\Console\\Kernel')->bootstrap();
            
            $db = $app->make('db');
            $connection = $db->connection();
            
            if ($connection->getDatabaseName() !== 'clockin_db') {
                $this->fail("Wrong database connected. Expected 'clockin_db', got '{$connection->getDatabaseName()}'");
            }
            
            if ($connection->getDriverName() !== 'mysql') {
                $this->fail("Wrong database driver. Expected 'mysql', got '{$connection->getDriverName()}'");
            }
            
            echo "✅ Database connection verified (MySQL - clockin_db)\n";
            
        } catch (Exception $e) {
            $this->fail("Database connection failed: " . $e->getMessage());
        }

        // Check if PHPUnit is available
        if (!class_exists('PHPUnit\\Framework\\TestCase')) {
            $this->fail("PHPUnit not found. Please run 'composer install --dev'.");
        }

        echo "✅ PHPUnit available\n";
        echo "✅ Prerequisites check completed\n\n";
    }

    private function runAllTests(): void
    {
        echo "🚀 Running integration tests...\n\n";

        foreach ($this->testSuites as $suiteName => $suiteClass) {
            $this->runTestSuite($suiteName, $suiteClass);
        }
    }

    private function runTestSuite(string $suiteName, string $suiteClass): void
    {
        echo "📋 Running {$suiteName} Tests...\n";
        echo str_repeat("-", 50) . "\n";

        $startTime = microtime(true);
        
        try {
            // Run PHPUnit for specific test class
            $command = "vendor/bin/phpunit --testdox --colors=never --filter=" . str_replace('\\', '\\\\', $suiteClass);
            $output = [];
            $returnCode = 0;
            
            exec($command . " 2>&1", $output, $returnCode);
            
            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);
            
            $this->parseTestResults($suiteName, $output, $returnCode, $duration);
            
        } catch (Exception $e) {
            $this->results[$suiteName] = [
                'status' => 'ERROR',
                'message' => $e->getMessage(),
                'duration' => 0,
                'tests' => 0
            ];
            echo "❌ ERROR: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    private function parseTestResults(string $suiteName, array $output, int $returnCode, float $duration): void
    {
        $outputText = implode("\n", $output);
        
        // Count tests
        $testCount = 0;
        $passedCount = 0;
        $failedCount = 0;
        
        // Parse PHPUnit output
        if (preg_match('/(\d+) tests?, (\d+) assertions?/', $outputText, $matches)) {
            $testCount = (int)$matches[1];
        }
        
        if ($returnCode === 0) {
            $passedCount = $testCount;
            $status = 'PASSED';
            echo "✅ All tests passed ({$testCount} tests)\n";
        } else {
            // Parse failures
            if (preg_match('/(\d+) failures?/', $outputText, $matches)) {
                $failedCount = (int)$matches[1];
                $passedCount = $testCount - $failedCount;
            } else {
                $failedCount = $testCount;
                $passedCount = 0;
            }
            $status = 'FAILED';
            echo "❌ Some tests failed ({$passedCount} passed, {$failedCount} failed)\n";
        }
        
        $this->results[$suiteName] = [
            'status' => $status,
            'duration' => $duration,
            'tests' => $testCount,
            'passed' => $passedCount,
            'failed' => $failedCount,
            'output' => $outputText
        ];
        
        $this->totalTests += $testCount;
        $this->passedTests += $passedCount;
        $this->failedTests += $failedCount;
        
        echo "⏱️  Duration: {$duration}s\n";
    }

    private function printSummary(): void
    {
        echo "================================================================================\n";
        echo "                                TEST SUMMARY                                   \n";
        echo "================================================================================\n\n";

        $totalDuration = 0;
        $allPassed = true;

        foreach ($this->results as $suiteName => $result) {
            $status = $result['status'];
            $icon = $status === 'PASSED' ? '✅' : ($status === 'FAILED' ? '❌' : '⚠️');
            $duration = $result['duration'];
            $totalDuration += $duration;
            
            if ($status !== 'PASSED') {
                $allPassed = false;
            }
            
            echo sprintf("%-30s %s %s (%.2fs)\n", $suiteName, $icon, $status, $duration);
            
            if (isset($result['tests'])) {
                echo sprintf("%-30s    Tests: %d, Passed: %d, Failed: %d\n", 
                    '', $result['tests'], $result['passed'], $result['failed']);
            }
            echo "\n";
        }

        echo "================================================================================\n";
        echo "OVERALL RESULTS:\n";
        echo "Total Test Suites: " . count($this->testSuites) . "\n";
        echo "Total Tests: {$this->totalTests}\n";
        echo "Passed: {$this->passedTests}\n";
        echo "Failed: {$this->failedTests}\n";
        echo "Total Duration: " . round($totalDuration, 2) . "s\n";
        echo "Success Rate: " . round(($this->passedTests / max($this->totalTests, 1)) * 100, 1) . "%\n";
        echo "================================================================================\n";

        if ($allPassed) {
            echo "🎉 ALL INTEGRATION TESTS PASSED! 🎉\n";
            echo "Your ClockIn application is ready for production with MySQL database.\n";
        } else {
            echo "⚠️  SOME TESTS FAILED\n";
            echo "Please review the failed tests and fix the issues before deploying.\n";
        }

        echo "\nDatabase URL: http://localhost:8080/phpmyadmin/index.php?route=/database/structure&db=clockin_db\n";
        echo "Completed: " . date('Y-m-d H:i:s') . "\n\n";
    }

    private function fail(string $message): void
    {
        echo "❌ FATAL ERROR: {$message}\n";
        echo "Please fix this issue and try again.\n\n";
        exit(1);
    }
}

// Run the integration tests
$runner = new IntegrationTestRunner();
$runner->run();
