<?php

namespace Tests\Feature\Integration;

use Tests\TestCase;
use Tests\Traits\DatabaseTestCase;
use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Assignment;
use App\Models\Verification;
use Illuminate\Support\Facades\DB;

class DatabaseIntegrationTest extends TestCase
{
    use DatabaseTestCase;

    /**
     * Test que toutes les tables existent
     */
    public function test_database_tables_exist(): void
    {
        $this->assertDatabaseTablesExist();
    }

    /**
     * Test des contraintes de clés étrangères
     */
    public function test_foreign_key_constraints_are_active(): void
    {
        $this->assertForeignKeyConstraints();
    }

    /**
     * Test de création d'un utilisateur
     */
    public function test_can_create_user(): void
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'role' => 'employee'
        ];

        $user = User::create($userData);

        $this->assertInstanceOf(User::class, $user);
        $this->assertDatabaseHasRecord('users', [
            'email' => '<EMAIL>',
            'role' => 'employee'
        ]);
    }

    /**
     * Test de création d'un site
     */
    public function test_can_create_site(): void
    {
        $siteData = [
            'name' => 'Chantier Test',
            'latitude' => 48.8566,
            'longitude' => 2.3522
        ];

        $site = Site::create($siteData);

        $this->assertInstanceOf(Site::class, $site);
        $this->assertDatabaseHasRecord('sites', $siteData);
    }

    /**
     * Test de création d'un pointage avec relations
     */
    public function test_can_create_pointage_with_relations(): void
    {
        $user = User::factory()->create();
        $site = Site::factory()->create();

        $pointageData = [
            'user_id' => $user->id,
            'site_id' => $site->id,
            'debut_pointage' => now(),
            'debut_latitude' => 48.8566,
            'debut_longitude' => 2.3522
        ];

        $pointage = Pointage::create($pointageData);

        $this->assertInstanceOf(Pointage::class, $pointage);
        $this->assertEquals($user->id, $pointage->user_id);
        $this->assertEquals($site->id, $pointage->site_id);
        
        // Vérifier les relations
        $this->assertInstanceOf(User::class, $pointage->user);
        $this->assertInstanceOf(Site::class, $pointage->site);
    }

    /**
     * Test de création d'une assignment
     */
    public function test_can_create_assignment(): void
    {
        $user = User::factory()->create();
        $site = Site::factory()->create();

        $assignment = Assignment::create([
            'user_id' => $user->id,
            'site_id' => $site->id
        ]);

        $this->assertInstanceOf(Assignment::class, $assignment);
        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $user->id,
            'site_id' => $site->id
        ]);
    }

    /**
     * Test de création d'une vérification
     */
    public function test_can_create_verification(): void
    {
        $user = User::factory()->create();

        $verificationData = [
            'user_id' => $user->id,
            'latitude' => 48.8566,
            'longitude' => 2.3522,
            'date_heure' => now()
        ];

        $verification = Verification::create($verificationData);

        $this->assertInstanceOf(Verification::class, $verification);
        $this->assertDatabaseHasRecord('verifications', [
            'user_id' => $user->id
        ]);
    }

    /**
     * Test de contrainte unique sur assignments
     */
    public function test_assignment_unique_constraint(): void
    {
        $user = User::factory()->create();
        $site = Site::factory()->create();

        // Première assignment
        Assignment::create([
            'user_id' => $user->id,
            'site_id' => $site->id
        ]);

        // Tentative de création d'une assignment dupliquée
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        Assignment::create([
            'user_id' => $user->id,
            'site_id' => $site->id
        ]);
    }

    /**
     * Test de suppression en cascade
     */
    public function test_cascade_delete_user(): void
    {
        $user = User::factory()->create();
        $site = Site::factory()->create();
        
        // Créer des enregistrements liés
        $pointage = Pointage::factory()->forUser($user)->forSite($site)->create();
        $assignment = Assignment::factory()->forUser($user)->forSite($site)->create();
        $verification = Verification::factory()->forUser($user)->create();

        $pointageId = $pointage->id;
        $assignmentId = $assignment->id;
        $verificationId = $verification->id;

        // Supprimer l'utilisateur
        $user->delete();

        // Vérifier que les enregistrements liés sont supprimés
        $this->assertDatabaseMissingRecord('pointages', ['id' => $pointageId]);
        $this->assertDatabaseMissingRecord('assignments', ['id' => $assignmentId]);
        $this->assertDatabaseMissingRecord('verifications', ['id' => $verificationId]);
    }

    /**
     * Test de suppression en cascade pour site
     */
    public function test_cascade_delete_site(): void
    {
        $user = User::factory()->create();
        $site = Site::factory()->create();
        
        $pointage = Pointage::factory()->forUser($user)->forSite($site)->create();
        $assignment = Assignment::factory()->forUser($user)->forSite($site)->create();

        $pointageId = $pointage->id;
        $assignmentId = $assignment->id;

        // Supprimer le site
        $site->delete();

        // Vérifier que les enregistrements liés sont supprimés
        $this->assertDatabaseMissingRecord('pointages', ['id' => $pointageId]);
        $this->assertDatabaseMissingRecord('assignments', ['id' => $assignmentId]);
    }

    /**
     * Test de performance avec de nombreux enregistrements
     */
    public function test_database_performance_with_many_records(): void
    {
        $startTime = microtime(true);

        // Créer 100 utilisateurs avec leurs données
        $users = User::factory()->count(10)->create();
        $sites = Site::factory()->count(5)->create();

        foreach ($users as $user) {
            foreach ($sites->take(2) as $site) {
                Assignment::factory()->forUserAndSite($user, $site)->create();
                Pointage::factory()->forUser($user)->forSite($site)->create();
            }
            Verification::factory()->forUser($user)->count(3)->create();
        }

        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;

        // Vérifier que l'opération prend moins de 5 secondes
        $this->assertLessThan(5.0, $executionTime, 
            "La création de données de test prend trop de temps: {$executionTime}s");

        // Vérifier les comptes
        $this->assertEquals(10, User::count());
        $this->assertEquals(5, Site::count());
        $this->assertEquals(20, Assignment::count());
        $this->assertEquals(20, Pointage::count());
        $this->assertEquals(30, Verification::count());
    }
}
