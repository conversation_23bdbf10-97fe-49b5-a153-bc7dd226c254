<?php

namespace Tests\Traits;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

trait DatabaseTestCase
{
    use RefreshDatabase;

    /**
     * Setup the test environment.
     */
    protected function setUp(): void
    {
        parent::setUp();
        
        // Démarrer une transaction pour chaque test
        DB::beginTransaction();
    }

    /**
     * Clean up the testing environment before the next test.
     */
    protected function tearDown(): void
    {
        // Rollback la transaction pour nettoyer les données
        DB::rollBack();
        
        parent::tearDown();
    }

    /**
     * Vérifier que toutes les tables existent
     */
    protected function assertDatabaseTablesExist(): void
    {
        $expectedTables = [
            'users',
            'sites', 
            'pointages',
            'assignments',
            'verifications',
            'logs',
            'personal_access_tokens'
        ];

        foreach ($expectedTables as $table) {
            $this->assertTrue(
                Schema::hasTable($table),
                "La table '{$table}' n'existe pas dans la base de données"
            );
        }
    }

    /**
     * Nettoyer toutes les données de test
     */
    protected function cleanTestData(): void
    {
        DB::table('logs')->delete();
        DB::table('verifications')->delete();
        DB::table('pointages')->delete();
        DB::table('assignments')->delete();
        DB::table('personal_access_tokens')->delete();
        DB::table('sites')->delete();
        DB::table('users')->where('email', 'LIKE', '%test%')->delete();
    }

    /**
     * Vérifier l'intégrité des contraintes de clés étrangères
     */
    protected function assertForeignKeyConstraints(): void
    {
        // Vérifier que les contraintes FK sont actives
        $this->assertTrue(
            DB::select("SELECT @@foreign_key_checks")[0]->{'@@foreign_key_checks'} == 1,
            "Les contraintes de clés étrangères ne sont pas activées"
        );
    }

    /**
     * Compter les enregistrements dans une table
     */
    protected function countRecords(string $table): int
    {
        return DB::table($table)->count();
    }

    /**
     * Vérifier qu'un enregistrement existe dans la base
     */
    protected function assertDatabaseHasRecord(string $table, array $data): void
    {
        $this->assertDatabaseHas($table, $data);
    }

    /**
     * Vérifier qu'un enregistrement n'existe pas dans la base
     */
    protected function assertDatabaseMissingRecord(string $table, array $data): void
    {
        $this->assertDatabaseMissing($table, $data);
    }
}
