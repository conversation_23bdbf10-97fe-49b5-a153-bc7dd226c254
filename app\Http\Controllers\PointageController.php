<?php

namespace App\Http\Controllers;

use App\Models\Pointage;
use App\Models\Site;
use App\Models\Assignment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class PointageController extends Controller
{
    /**
     * Liste des pointages de l'utilisateur connecté
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = $user->pointages()->with('site');

        // Filtrage par date
        if ($request->has('date')) {
            $query->whereDate('debut_pointage', $request->date);
        }

        $pointages = $query->orderBy('debut_pointage', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $pointages
        ]);
    }

    /**
     * Pointage en cours de l'utilisateur
     */
    public function current(Request $request): JsonResponse
    {
        $user = $request->user();
        $pointage = $user->pointages()
                         ->whereNull('fin_pointage')
                         ->with('site')
                         ->first();

        return response()->json([
            'success' => true,
            'data' => [
                'pointage' => $pointage
            ]
        ]);
    }

    /**
     * Commencer un pointage
     */
    public function start(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'site_id' => 'required|exists:sites,id',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $site = Site::find($request->site_id);

        // Vérifier si l'utilisateur est assigné au site
        $assignment = Assignment::where('user_id', $user->id)
                                ->where('site_id', $site->id)
                                ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas assigné à ce site'
            ], 403);
        }

        // Vérifier s'il n'y a pas déjà un pointage en cours
        $activePointage = $user->pointages()->whereNull('fin_pointage')->first();
        if ($activePointage) {
            return response()->json([
                'success' => false,
                'message' => 'Un pointage est déjà en cours'
            ], 400);
        }

        // Vérifier la distance
        $distance = $site->distanceFrom($request->latitude, $request->longitude);
        if ($distance > 100) { // 100 mètres de tolérance
            return response()->json([
                'success' => false,
                'message' => 'Vous êtes trop éloigné du site pour pointer'
            ], 400);
        }

        $pointage = Pointage::create([
            'user_id' => $user->id,
            'site_id' => $site->id,
            'debut_pointage' => now(),
            'debut_latitude' => $request->latitude,
            'debut_longitude' => $request->longitude
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'pointage' => $pointage->load('site')
            ],
            'message' => 'Pointage commencé avec succès'
        ], 201);
    }

    /**
     * Terminer un pointage
     */
    public function end(Request $request, Pointage $pointage): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        // Vérifier que le pointage appartient à l'utilisateur
        if ($pointage->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        // Vérifier que le pointage n'est pas déjà terminé
        if ($pointage->fin_pointage) {
            return response()->json([
                'success' => false,
                'message' => 'Ce pointage est déjà terminé'
            ], 400);
        }

        $pointage->terminer($request->latitude, $request->longitude);

        return response()->json([
            'success' => true,
            'data' => [
                'pointage' => $pointage->fresh()->load('site')
            ],
            'message' => 'Pointage terminé avec succès'
        ]);
    }

    /**
     * Afficher un pointage spécifique
     */
    public function show(Request $request, Pointage $pointage): JsonResponse
    {
        $user = $request->user();

        // Vérifier les autorisations
        if ($pointage->user_id !== $user->id && !$user->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'pointage' => $pointage->load('site', 'user')
            ]
        ]);
    }

    /**
     * Statistiques des pointages de l'utilisateur
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $totalPointages = $user->pointages()->count();
        $pointagesEnCours = $user->pointages()->whereNull('fin_pointage')->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_pointages' => $totalPointages,
                'pointages_en_cours' => $pointagesEnCours,
                'temps_total_travaille' => null, // À calculer si nécessaire
                'moyenne_duree_pointage' => null // À calculer si nécessaire
            ]
        ]);
    }

    /**
     * Tous les pointages (admin uniquement)
     */
    public function adminIndex(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $pointages = Pointage::with('user', 'site')
                            ->orderBy('debut_pointage', 'desc')
                            ->get();

        return response()->json([
            'success' => true,
            'data' => $pointages
        ]);
    }

    /**
     * Pointages actifs (admin uniquement)
     */
    public function activePointages(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $pointages = Pointage::whereNull('fin_pointage')
                            ->with('user', 'site')
                            ->orderBy('debut_pointage', 'desc')
                            ->get();

        return response()->json([
            'success' => true,
            'data' => $pointages
        ]);
    }
}
