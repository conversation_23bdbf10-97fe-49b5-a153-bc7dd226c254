<?php

/**
 * Script pour exécuter les tests d'intégration
 * 
 * Usage:
 * php run-integration-tests.php [options]
 * 
 * Options:
 * --setup     : Préparer la base de données uniquement
 * --cleanup   : Nettoyer la base de données uniquement  
 * --validate  : Valider la structure de la base de données
 * --coverage  : Générer un rapport de couverture
 * --help      : Afficher cette aide
 */

require_once __DIR__ . '/vendor/autoload.php';

use Tests\TestHelper;

// Charger l'environnement Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

function showHelp()
{
    echo "\n=== Script de Tests d'Intégration ClockIn ===\n\n";
    echo "Usage: php run-integration-tests.php [options]\n\n";
    echo "Options disponibles:\n";
    echo "  --setup     Préparer la base de données uniquement\n";
    echo "  --cleanup   Nettoyer la base de données uniquement\n";
    echo "  --validate  Valider la structure de la base de données\n";
    echo "  --coverage  Générer un rapport de couverture\n";
    echo "  --seed      Créer des données de test de base\n";
    echo "  --all       Exécuter tous les tests (défaut)\n";
    echo "  --help      Afficher cette aide\n\n";
    echo "Exemples:\n";
    echo "  php run-integration-tests.php --setup\n";
    echo "  php run-integration-tests.php --all\n";
    echo "  php run-integration-tests.php --coverage\n\n";
}

function checkRequirements()
{
    echo "Vérification des prérequis...\n";
    
    // Vérifier PHP
    if (version_compare(PHP_VERSION, '8.2.0', '<')) {
        throw new Exception("PHP 8.2+ requis. Version actuelle: " . PHP_VERSION);
    }
    echo "✓ PHP version: " . PHP_VERSION . "\n";
    
    // Vérifier les extensions
    $requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'openssl', 'json'];
    foreach ($requiredExtensions as $ext) {
        if (!extension_loaded($ext)) {
            throw new Exception("Extension PHP requise manquante: {$ext}");
        }
    }
    echo "✓ Extensions PHP requises présentes\n";
    
    // Vérifier les fichiers de configuration
    if (!file_exists(__DIR__ . '/.env')) {
        throw new Exception("Fichier .env manquant");
    }
    echo "✓ Fichier .env présent\n";
    
    // Vérifier la configuration de test
    if (env('APP_ENV') !== 'testing') {
        echo "⚠ APP_ENV n'est pas défini sur 'testing'\n";
    }
    
    echo "Prérequis vérifiés avec succès.\n\n";
}

function runSpecificTests($testFile)
{
    echo "Exécution de: {$testFile}\n";
    
    $command = "php artisan test {$testFile} --env=testing --verbose";
    $output = [];
    $returnCode = 0;
    
    exec($command, $output, $returnCode);
    
    foreach ($output as $line) {
        echo $line . "\n";
    }
    
    if ($returnCode !== 0) {
        echo "❌ Échec du test: {$testFile}\n";
        return false;
    }
    
    echo "✅ Test réussi: {$testFile}\n\n";
    return true;
}

function main($argv)
{
    $options = array_slice($argv, 1);
    
    if (empty($options) || in_array('--help', $options)) {
        showHelp();
        return;
    }
    
    try {
        checkRequirements();
        
        if (in_array('--setup', $options)) {
            echo "=== Préparation de la base de données ===\n";
            TestHelper::setupDatabase();
            echo "Base de données préparée avec succès.\n";
            return;
        }
        
        if (in_array('--cleanup', $options)) {
            echo "=== Nettoyage de la base de données ===\n";
            TestHelper::cleanupDatabase();
            echo "Base de données nettoyée avec succès.\n";
            return;
        }
        
        if (in_array('--validate', $options)) {
            echo "=== Validation de la structure ===\n";
            TestHelper::validateTestData();
            return;
        }
        
        if (in_array('--seed', $options)) {
            echo "=== Création des données de test ===\n";
            TestHelper::seedTestData();
            return;
        }
        
        if (in_array('--coverage', $options)) {
            echo "=== Génération du rapport de couverture ===\n";
            TestHelper::generateCoverageReport();
            return;
        }
        
        // Exécution complète des tests
        echo "=== Exécution complète des tests d'intégration ===\n\n";
        
        // Préparer la base de données
        TestHelper::setupDatabase();
        
        // Liste des tests à exécuter dans l'ordre
        $testSuites = [
            'tests/Feature/Integration/DatabaseIntegrationTest.php' => 'Tests de base de données',
            'tests/Feature/Auth/AuthenticationIntegrationTest.php' => 'Tests d\'authentification',
            'tests/Feature/Site/SiteIntegrationTest.php' => 'Tests des sites',
            'tests/Feature/Employee/EmployeeIntegrationTest.php' => 'Tests des employés',
            'tests/Feature/Assignment/AssignmentIntegrationTest.php' => 'Tests des assignments',
            'tests/Feature/Pointage/PointageIntegrationTest.php' => 'Tests des pointages',
            'tests/Feature/Verification/VerificationIntegrationTest.php' => 'Tests des vérifications',
            'tests/Feature/Integration/CompleteWorkflowIntegrationTest.php' => 'Tests de workflow complet'
        ];
        
        $totalTests = count($testSuites);
        $passedTests = 0;
        $failedTests = [];
        
        foreach ($testSuites as $testFile => $description) {
            echo "📋 {$description}\n";
            echo str_repeat('-', 50) . "\n";
            
            if (runSpecificTests($testFile)) {
                $passedTests++;
            } else {
                $failedTests[] = $testFile;
            }
        }
        
        // Résumé final
        echo "\n" . str_repeat('=', 60) . "\n";
        echo "RÉSUMÉ DES TESTS D'INTÉGRATION\n";
        echo str_repeat('=', 60) . "\n";
        echo "Total des suites de tests: {$totalTests}\n";
        echo "✅ Réussies: {$passedTests}\n";
        echo "❌ Échouées: " . count($failedTests) . "\n";
        
        if (!empty($failedTests)) {
            echo "\nTests échoués:\n";
            foreach ($failedTests as $test) {
                echo "  - {$test}\n";
            }
            echo "\nVeuillez vérifier les erreurs ci-dessus.\n";
            exit(1);
        } else {
            echo "\n🎉 Tous les tests d'intégration ont réussi!\n";
            echo "\nPour utiliser ces tests avec Postman:\n";
            echo "1. Démarrez le serveur: php artisan serve\n";
            echo "2. Importez la collection Postman (si disponible)\n";
            echo "3. Configurez l'URL de base: http://localhost:8000/api\n";
            echo "4. Utilisez les tokens d'authentification générés par les tests\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Erreur: " . $e->getMessage() . "\n";
        exit(1);
    }
}

// Point d'entrée
main($argv);
