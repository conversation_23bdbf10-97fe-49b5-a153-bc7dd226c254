<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Assignment;
use App\Models\Verification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class AdminController extends Controller
{
    /**
     * Tableau de bord administrateur
     */
    public function dashboard(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $totalEmployees = User::where('role', 'employee')->count();
        $totalSites = Site::count();
        $activePointages = Pointage::whereNull('fin_pointage')->count();
        $totalPointagesToday = Pointage::whereDate('debut_pointage', Carbon::today())->count();
        $totalAssignments = Assignment::count();
        $totalVerificationsToday = Verification::whereDate('date_heure', Carbon::today())->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_employees' => $totalEmployees,
                'total_sites' => $totalSites,
                'active_pointages' => $activePointages,
                'total_pointages_today' => $totalPointagesToday,
                'total_assignments' => $totalAssignments,
                'total_verifications_today' => $totalVerificationsToday
            ]
        ]);
    }

    /**
     * Générer un rapport
     */
    public function generateReport(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'include_employees' => 'boolean',
            'include_sites' => 'boolean',
            'include_pointages' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        $report = [
            'period' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'duration_days' => $startDate->diffInDays($endDate) + 1
            ]
        ];

        if ($request->include_employees) {
            $employees = User::where('role', 'employee')->get();
            $report['employees_summary'] = [
                'total_employees' => $employees->count(),
                'employees_with_pointages' => $employees->whereHas('pointages', function($query) use ($startDate, $endDate) {
                    $query->whereBetween('debut_pointage', [$startDate, $endDate]);
                })->count()
            ];
        }

        if ($request->include_sites) {
            $sites = Site::all();
            $report['sites_summary'] = [
                'total_sites' => $sites->count(),
                'sites_with_activity' => $sites->whereHas('pointages', function($query) use ($startDate, $endDate) {
                    $query->whereBetween('debut_pointage', [$startDate, $endDate]);
                })->count()
            ];
        }

        if ($request->include_pointages) {
            $pointages = Pointage::whereBetween('debut_pointage', [$startDate, $endDate]);
            $report['pointages_summary'] = [
                'total_pointages' => $pointages->count(),
                'completed_pointages' => $pointages->whereNotNull('fin_pointage')->count(),
                'active_pointages' => $pointages->whereNull('fin_pointage')->count()
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'report' => $report
            ],
            'message' => 'Rapport généré avec succès'
        ]);
    }
}
