<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Pointage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'site_id',
        'debut_pointage',
        'fin_pointage',
        'duree',
        'debut_latitude',
        'debut_longitude',
        'fin_latitude',
        'fin_longitude',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'debut_pointage' => 'datetime',
        'fin_pointage' => 'datetime',
        'debut_latitude' => 'decimal:8',
        'debut_longitude' => 'decimal:8',
        'fin_latitude' => 'decimal:8',
        'fin_longitude' => 'decimal:8',
    ];

    /**
     * Relation avec l'utilisateur
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relation avec le site
     */
    public function site(): BelongsTo
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Vérifier si le pointage est en cours
     */
    public function isEnCours(): bool
    {
        return is_null($this->fin_pointage);
    }

    /**
     * Calculer la durée du pointage
     */
    public function calculerDuree(): ?string
    {
        if (!$this->fin_pointage) {
            return null;
        }

        $debut = Carbon::parse($this->debut_pointage);
        $fin = Carbon::parse($this->fin_pointage);

        return $debut->diff($fin)->format('%H:%I:%S');
    }

    /**
     * Terminer le pointage
     */
    public function terminer(?float $latitude = null, ?float $longitude = null): void
    {
        $this->fin_pointage = now();
        $this->fin_latitude = $latitude;
        $this->fin_longitude = $longitude;
        $this->duree = $this->calculerDuree();
        $this->save();
    }
}
