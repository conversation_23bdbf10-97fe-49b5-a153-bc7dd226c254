<?php

namespace Tests\Feature\Employee;

use Tests\TestCase;
use Tests\Traits\DatabaseTestCase;
use Tests\Traits\AuthenticationHelpers;
use App\Models\User;
use App\Models\Site;
use App\Models\Assignment;
use App\Models\Pointage;
use Illuminate\Support\Facades\Hash;

class EmployeeIntegrationTest extends TestCase
{
    use DatabaseTestCase, AuthenticationHelpers;

    /**
     * Test qu'un admin peut récupérer tous les employés
     */
    public function test_admin_can_get_all_employees(): void
    {
        $admin = $this->createTestAdmin();
        
        User::factory()->count(5)->create(['role' => 'employee']);

        $response = $this->authenticatedGet('/api/employees', $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'email',
                            'role',
                            'created_at',
                            'updated_at'
                        ]
                    ]
                ]);

        $employees = $response->json('data');
        $this->assertCount(5, $employees);
        
        foreach ($employees as $employee) {
            $this->assertEquals('employee', $employee['role']);
        }
    }

    /**
     * Test qu'un employé ne peut pas voir la liste des employés
     */
    public function test_employee_cannot_get_all_employees(): void
    {
        $employee = $this->createTestUser();

        $response = $this->authenticatedGet('/api/employees', $employee);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Accès non autorisé'
                ]);
    }

    /**
     * Test de création d'un employé par un admin
     */
    public function test_admin_can_create_employee(): void
    {
        $admin = $this->createTestAdmin();

        $employeeData = [
            'name' => 'Nouvel Employé',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'employee'
        ];

        $response = $this->authenticatedPost('/api/employees', $employeeData, $admin);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'employee' => [
                            'id',
                            'name',
                            'email',
                            'role'
                        ]
                    ],
                    'message'
                ]);

        $this->assertDatabaseHasRecord('users', [
            'email' => '<EMAIL>',
            'role' => 'employee'
        ]);
    }

    /**
     * Test de récupération d'un employé spécifique
     */
    public function test_admin_can_get_specific_employee(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'role' => 'employee'
        ]);

        $response = $this->authenticatedGet("/api/employees/{$employee->id}", $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'employee' => [
                            'id' => $employee->id,
                            'name' => 'John Doe',
                            'email' => '<EMAIL>',
                            'role' => 'employee'
                        ]
                    ]
                ]);
    }

    /**
     * Test de mise à jour d'un employé
     */
    public function test_admin_can_update_employee(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);

        $updateData = [
            'name' => 'Nom Modifié',
            'email' => '<EMAIL>'
        ];

        $response = $this->authenticatedPut("/api/employees/{$employee->id}", $updateData, $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'employee' => $updateData
                    ]
                ]);

        $this->assertDatabaseHasRecord('users', array_merge(['id' => $employee->id], $updateData));
    }

    /**
     * Test de suppression d'un employé
     */
    public function test_admin_can_delete_employee(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);

        $response = $this->authenticatedDelete("/api/employees/{$employee->id}", $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Employé supprimé avec succès'
                ]);

        $this->assertDatabaseMissingRecord('users', ['id' => $employee->id]);
    }

    /**
     * Test qu'un employé ne peut pas supprimer un autre employé
     */
    public function test_employee_cannot_delete_other_employee(): void
    {
        $employee1 = $this->createTestUser();
        $employee2 = User::factory()->create(['role' => 'employee']);

        $response = $this->authenticatedDelete("/api/employees/{$employee2->id}", $employee1);

        $response->assertStatus(403);
    }

    /**
     * Test de validation des données d'employé
     */
    public function test_employee_creation_validation(): void
    {
        $admin = $this->createTestAdmin();

        $invalidData = [
            'name' => '',
            'email' => 'invalid-email',
            'password' => '123',
            'role' => 'invalid_role'
        ];

        $response = $this->authenticatedPost('/api/employees', $invalidData, $admin);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'email', 'password', 'role']);
    }

    /**
     * Test d'assignation d'un employé à un site
     */
    public function test_admin_can_assign_employee_to_site(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();

        $assignmentData = [
            'site_id' => $site->id
        ];

        $response = $this->authenticatedPost("/api/employees/{$employee->id}/assign", $assignmentData, $admin);

        $response->assertStatus(201)
                ->assertJson([
                    'success' => true,
                    'message' => 'Employé assigné au site avec succès'
                ]);

        $this->assertDatabaseHasRecord('assignments', [
            'user_id' => $employee->id,
            'site_id' => $site->id
        ]);
    }

    /**
     * Test de désassignation d'un employé d'un site
     */
    public function test_admin_can_unassign_employee_from_site(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();
        
        $assignment = Assignment::factory()->forUserAndSite($employee, $site)->create();

        $response = $this->authenticatedDelete("/api/employees/{$employee->id}/unassign/{$site->id}", $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Employé désassigné du site avec succès'
                ]);

        $this->assertDatabaseMissingRecord('assignments', ['id' => $assignment->id]);
    }

    /**
     * Test de récupération des sites assignés à un employé
     */
    public function test_can_get_employee_assigned_sites(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site1 = Site::factory()->create(['name' => 'Site 1']);
        $site2 = Site::factory()->create(['name' => 'Site 2']);

        Assignment::factory()->forUserAndSite($employee, $site1)->create();
        Assignment::factory()->forUserAndSite($employee, $site2)->create();

        $response = $this->authenticatedGet("/api/employees/{$employee->id}/sites", $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'latitude',
                            'longitude'
                        ]
                    ]
                ]);

        $this->assertCount(2, $response->json('data'));
    }

    /**
     * Test de récupération des pointages d'un employé
     */
    public function test_admin_can_get_employee_pointages(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();

        Pointage::factory()->forUser($employee)->forSite($site)->count(3)->create();

        $response = $this->authenticatedGet("/api/employees/{$employee->id}/pointages", $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'site',
                            'debut_pointage',
                            'fin_pointage',
                            'duree'
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('data'));
    }

    /**
     * Test de recherche d'employés
     */
    public function test_admin_can_search_employees(): void
    {
        $admin = $this->createTestAdmin();
        
        User::factory()->create(['name' => 'John Doe', 'role' => 'employee']);
        User::factory()->create(['name' => 'Jane Smith', 'role' => 'employee']);
        User::factory()->create(['name' => 'Bob Johnson', 'role' => 'employee']);

        $response = $this->authenticatedGet('/api/employees?search=John', $admin);

        $response->assertStatus(200);
        
        $employees = $response->json('data');
        $this->assertCount(2, $employees); // John Doe et Bob Johnson
        
        foreach ($employees as $employee) {
            $this->assertStringContainsString('John', $employee['name']);
        }
    }

    /**
     * Test de changement de mot de passe d'un employé par un admin
     */
    public function test_admin_can_change_employee_password(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);

        $passwordData = [
            'new_password' => 'newpassword123',
            'new_password_confirmation' => 'newpassword123'
        ];

        $response = $this->authenticatedPut("/api/employees/{$employee->id}/password", $passwordData, $admin);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Mot de passe modifié avec succès'
                ]);

        $employee->refresh();
        $this->assertTrue(Hash::check('newpassword123', $employee->password));
    }

    /**
     * Test de récupération des statistiques d'un employé
     */
    public function test_admin_can_get_employee_statistics(): void
    {
        $admin = $this->createTestAdmin();
        $employee = User::factory()->create(['role' => 'employee']);
        $site = Site::factory()->create();

        Assignment::factory()->forUserAndSite($employee, $site)->create();
        Pointage::factory()->forUser($employee)->forSite($site)->termine()->count(5)->create();
        Pointage::factory()->forUser($employee)->forSite($site)->enCours()->create();

        $response = $this->authenticatedGet("/api/employees/{$employee->id}/statistics", $admin);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_sites_assigned',
                        'total_pointages',
                        'pointages_en_cours',
                        'temps_total_travaille',
                        'moyenne_duree_pointage'
                    ]
                ]);

        $stats = $response->json('data');
        $this->assertEquals(1, $stats['total_sites_assigned']);
        $this->assertEquals(6, $stats['total_pointages']);
        $this->assertEquals(1, $stats['pointages_en_cours']);
    }
}
