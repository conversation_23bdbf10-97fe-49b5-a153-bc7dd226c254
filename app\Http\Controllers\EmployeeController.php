<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Site;
use App\Models\Assignment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class EmployeeController extends Controller
{
    /**
     * Liste de tous les employés (admin uniquement)
     */
    public function index(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $query = User::where('role', 'employee');

        // Recherche par nom
        if ($request->has('search')) {
            $query->where('name', 'LIKE', '%' . $request->search . '%');
        }

        $employees = $query->get();

        return response()->json([
            'success' => true,
            'data' => $employees
        ]);
    }

    /**
     * Créer un nouvel employé (admin uniquement)
     */
    public function store(Request $request): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'sometimes|in:employee'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $employee = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'employee'
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'employee' => $employee
            ],
            'message' => 'Employé créé avec succès'
        ], 201);
    }

    /**
     * Afficher un employé spécifique (admin uniquement)
     */
    public function show(Request $request, User $employee): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'employee' => $employee
            ]
        ]);
    }

    /**
     * Mettre à jour un employé (admin uniquement)
     */
    public function update(Request $request, User $employee): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,' . $employee->id
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $employee->update($request->only(['name', 'email']));

        return response()->json([
            'success' => true,
            'data' => [
                'employee' => $employee->fresh()
            ],
            'message' => 'Employé mis à jour avec succès'
        ]);
    }

    /**
     * Supprimer un employé (admin uniquement)
     */
    public function destroy(Request $request, User $employee): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $employee->delete();

        return response()->json([
            'success' => true,
            'message' => 'Employé supprimé avec succès'
        ]);
    }

    /**
     * Assigner un employé à un site (admin uniquement)
     */
    public function assignToSite(Request $request, User $employee): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'site_id' => 'required|exists:sites,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        // Vérifier si l'assignment existe déjà
        $existingAssignment = Assignment::where('user_id', $employee->id)
                                       ->where('site_id', $request->site_id)
                                       ->first();

        if ($existingAssignment) {
            return response()->json([
                'success' => false,
                'message' => 'Cet employé est déjà assigné à ce site'
            ], 422);
        }

        Assignment::create([
            'user_id' => $employee->id,
            'site_id' => $request->site_id
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Employé assigné au site avec succès'
        ], 201);
    }

    /**
     * Désassigner un employé d'un site (admin uniquement)
     */
    public function unassignFromSite(Request $request, User $employee, Site $site): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $assignment = Assignment::where('user_id', $employee->id)
                                ->where('site_id', $site->id)
                                ->first();

        if (!$assignment) {
            return response()->json([
                'success' => false,
                'message' => 'Cet employé n\'est pas assigné à ce site'
            ], 404);
        }

        $assignment->delete();

        return response()->json([
            'success' => true,
            'message' => 'Employé désassigné du site avec succès'
        ]);
    }

    /**
     * Sites assignés à un employé (admin uniquement)
     */
    public function assignedSites(Request $request, User $employee): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $sites = $employee->assignments()->with('site')->get()->pluck('site');

        return response()->json([
            'success' => true,
            'data' => $sites
        ]);
    }

    /**
     * Pointages d'un employé (admin uniquement)
     */
    public function pointages(Request $request, User $employee): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $pointages = $employee->pointages()->with('site')->orderBy('debut_pointage', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $pointages
        ]);
    }

    /**
     * Statistiques d'un employé (admin uniquement)
     */
    public function statistics(Request $request, User $employee): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $totalSitesAssigned = $employee->assignments()->count();
        $totalPointages = $employee->pointages()->count();
        $pointagesEnCours = $employee->pointages()->whereNull('fin_pointage')->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_sites_assigned' => $totalSitesAssigned,
                'total_pointages' => $totalPointages,
                'pointages_en_cours' => $pointagesEnCours,
                'temps_total_travaille' => null,
                'moyenne_duree_pointage' => null
            ]
        ]);
    }

    /**
     * Changer le mot de passe d'un employé (admin uniquement)
     */
    public function changePassword(Request $request, User $employee): JsonResponse
    {
        if (!$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès non autorisé'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'new_password' => 'required|string|min:8|confirmed'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Erreurs de validation',
                'errors' => $validator->errors()
            ], 422);
        }

        $employee->update([
            'password' => Hash::make($request->new_password)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Mot de passe modifié avec succès'
        ]);
    }
}
